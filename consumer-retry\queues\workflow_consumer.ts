import taskWorkflow from "producer/workflow/task_workflow"
import { generateImagesFromBrochures } from "producer/handlers/generate_images_from_brochures"

export default {
  async queue(batch: any, env: any) {
    if (!env.TASK_STATUS) {
      console.error('TASK_STATUS KV binding is missing from env:', env);
      return;
    }
    if (!batch || !batch.messages || !Array.isArray(batch.messages)) {
      console.error('Queue batch missing messages array:', JSON.stringify(batch));
      return;
    }
    for (const message of batch.messages) {
      if (!message.body) {
        console.error('Queue message missing body:', JSON.stringify(message));
        await env.TASK_STATUS.put('status:unknown', JSON.stringify({ status: 'failed', error: 'Message body is undefined' }));
        continue;
      }
      const { task_id, payload, project_key, action } = message.body;
      console.log(`Processing task: ${task_id}`);
      let status = "processing";
      let loadtime = null;
      let result = null;
      try {
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "processing" }));
        const startTime = Date.now();
        if(action === "generate_brochure_images"){
          result = await taskWorkflow.runWorkflow(payload, env, task_id);
        }else if(action === "generate_banner_images"){
          result = await taskWorkflow.runWorkflow_banner(payload, env, task_id);
        }
        const loadtimeMs = Date.now() - startTime;
        loadtime = parseFloat((loadtimeMs / 60000).toFixed(2)); // menit, dua desimal
        await env.TASK_STATUS.put(`result:${task_id}`, JSON.stringify(result));
        //await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "completed" }));
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "completed", loadtime: loadtime+' minutes' }));
        status = "completed";
      } catch (e: any) {
        console.error(`Error task ${task_id}`, e);
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "failed", error: e.message }));
        status = "failed";
        // Auto-retry: push to QUEUE_RETRY if last_success_agent is present
        if (e && e.last_success_agent && env.QUEUE_RETRY) {
          try {
            await env.QUEUE_RETRY.send({
              ...message.body,
              last_success_agent: e.last_success_agent
            });
            console.log(`Task ${task_id} pushed to QUEUE_RETRY with last_success_agent: ${e.last_success_agent}`);
          } catch (retryErr) {
            console.error(`Failed to push task ${task_id} to QUEUE_RETRY`, retryErr);
          }
        }
      }
      // Kirim status ke webhook eksternal
      try {
        await fetch("https://ai.gass.co.id/webhook.html", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            act: action,
            status: status,
            task_id,
            project_key,
            loadtime: loadtime
          })
        });
      } catch (webhookErr) {
        console.error("Failed to send webhook notification", webhookErr);
      }
    }
  }
}
