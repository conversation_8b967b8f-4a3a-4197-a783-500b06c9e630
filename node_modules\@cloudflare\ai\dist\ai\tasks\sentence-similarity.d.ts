import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiSentenceSimilarityInput, BaseAiSentenceSimilarity } from "./types/tasks";
export declare class AiSentenceSimilarity extends BaseAiSentenceSimilarity implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                source: {
                    type: string;
                    minLength: number;
                };
                sentences: {
                    type: string;
                    items: {
                        type: string;
                        minLength: number;
                    };
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            items: {
                type: string;
            };
        };
    };
    constructor(inputs: AiSentenceSimilarityInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=sentence-similarity.d.ts.map