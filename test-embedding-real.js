// Real API test script for embedding functionality
// Run with: OPENAI_API_KEY=your_key node test-embedding-real.js

const OpenAI = require('openai');
const crypto = require('crypto');

// Configuration
const config = {
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  MODEL: 'text-embedding-ada-002',
  EXPECTED_DIMENSIONS: 1536
};

// Real getEmbedding implementation
async function getEmbedding(text, apiKey) {
  if (!apiKey) {
    throw new Error('OpenAI API key is required');
  }
  
  const openai = new OpenAI({ apiKey });
  
  try {
    console.log(`🔄 Generating embedding for: "${text.substring(0, 50)}..."`);
    
    const embeddingRes = await openai.embeddings.create({
      model: config.MODEL,
      input: text
    });
    
    const embedding = embeddingRes.data[0].embedding;
    console.log(`✅ Embedding generated successfully (${embedding.length} dimensions)`);
    
    return embedding;
    
  } catch (error) {
    console.error(`❌ OpenAI API error: ${error.message}`);
    throw error;
  }
}

// Cosine similarity calculation
function cosineSimilarity(a, b) {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same dimensions');
  }
  
  let dot = 0, normA = 0, normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dot += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  return dot / (Math.sqrt(normA) * Math.sqrt(normB));
}

// Test 1: Basic embedding generation and validation
async function testBasicEmbedding() {
  console.log('\n🧪 Test 1: Basic Embedding Generation');
  console.log('====================================');
  
  const testTexts = [
    "Machine learning is a subset of artificial intelligence",
    "Deep learning menggunakan neural networks dengan banyak layer",
    "Cloudflare Workers adalah platform serverless untuk edge computing",
    "Vector databases memungkinkan pencarian similarity yang efisien"
  ];
  
  const embeddings = [];
  
  for (const text of testTexts) {
    console.log(`\nProcessing: "${text}"`);
    
    const startTime = Date.now();
    const embedding = await getEmbedding(text, config.OPENAI_API_KEY);
    const duration = Date.now() - startTime;
    
    // Validate embedding
    console.log(`📊 Validation Results:`);
    console.log(`   - Type: ${typeof embedding}`);
    console.log(`   - Is Array: ${Array.isArray(embedding)}`);
    console.log(`   - Dimensions: ${embedding.length}`);
    console.log(`   - Generation time: ${duration}ms`);
    console.log(`   - Sample values: [${embedding.slice(0, 3).map(v => v.toFixed(6)).join(', ')}...]`);
    
    // Check value ranges
    const minVal = Math.min(...embedding);
    const maxVal = Math.max(...embedding);
    console.log(`   - Value range: ${minVal.toFixed(6)} to ${maxVal.toFixed(6)}`);
    
    // Validation checks
    if (!Array.isArray(embedding)) {
      throw new Error('Embedding must be an array');
    }
    if (embedding.length !== config.EXPECTED_DIMENSIONS) {
      throw new Error(`Expected ${config.EXPECTED_DIMENSIONS} dimensions, got ${embedding.length}`);
    }
    if (embedding.some(val => typeof val !== 'number' || isNaN(val))) {
      throw new Error('All embedding values must be valid numbers');
    }
    
    embeddings.push({ text, embedding, duration });
    
    // Rate limiting delay
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n✅ Generated ${embeddings.length} embeddings successfully!`);
  return embeddings;
}

// Test 2: Similarity testing
async function testSimilarity() {
  console.log('\n🧪 Test 2: Similarity Testing');
  console.log('=============================');
  
  const similarPairs = [
    {
      text1: "Machine learning is a branch of artificial intelligence",
      text2: "AI includes machine learning as a subset",
      expectedSimilarity: "high"
    },
    {
      text1: "Deep learning uses neural networks",
      text2: "Neural networks are used in deep learning",
      expectedSimilarity: "high"
    },
    {
      text1: "Cloudflare provides CDN services",
      text2: "Cooking pasta requires boiling water",
      expectedSimilarity: "low"
    }
  ];
  
  for (const pair of similarPairs) {
    console.log(`\nTesting similarity:`);
    console.log(`Text 1: "${pair.text1}"`);
    console.log(`Text 2: "${pair.text2}"`);
    console.log(`Expected: ${pair.expectedSimilarity} similarity`);
    
    const embedding1 = await getEmbedding(pair.text1, config.OPENAI_API_KEY);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const embedding2 = await getEmbedding(pair.text2, config.OPENAI_API_KEY);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const similarity = cosineSimilarity(embedding1, embedding2);
    
    console.log(`📊 Similarity Score: ${similarity.toFixed(4)}`);
    
    // Validate expectations
    if (pair.expectedSimilarity === "high" && similarity > 0.7) {
      console.log(`✅ High similarity confirmed (${similarity.toFixed(4)} > 0.7)`);
    } else if (pair.expectedSimilarity === "low" && similarity < 0.5) {
      console.log(`✅ Low similarity confirmed (${similarity.toFixed(4)} < 0.5)`);
    } else {
      console.log(`⚠️  Unexpected similarity: ${similarity.toFixed(4)} for ${pair.expectedSimilarity} expectation`);
    }
  }
}

// Test 3: Consistency testing
async function testConsistency() {
  console.log('\n🧪 Test 3: Consistency Testing');
  console.log('==============================');
  
  const testText = "Consistency test for embedding generation with OpenAI API";
  console.log(`Testing consistency for: "${testText}"`);
  
  console.log('\nGenerating first embedding...');
  const embedding1 = await getEmbedding(testText, config.OPENAI_API_KEY);
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('Generating second embedding...');
  const embedding2 = await getEmbedding(testText, config.OPENAI_API_KEY);
  
  const similarity = cosineSimilarity(embedding1, embedding2);
  
  console.log(`📊 Consistency Results:`);
  console.log(`   - Similarity: ${similarity.toFixed(6)}`);
  console.log(`   - Identical: ${similarity === 1.0 ? 'Yes' : 'No'}`);
  
  if (similarity === 1.0) {
    console.log('✅ Perfect consistency - embeddings are identical');
  } else if (similarity > 0.999) {
    console.log('✅ Very high consistency - minor floating point differences');
  } else {
    console.log(`⚠️  Lower consistency than expected: ${similarity.toFixed(6)}`);
  }
  
  return { similarity, embedding1, embedding2 };
}

// Test 4: Cross-language testing
async function testCrossLanguage() {
  console.log('\n🧪 Test 4: Cross-Language Testing');
  console.log('=================================');
  
  const languagePairs = [
    {
      english: "Artificial intelligence is transforming technology",
      indonesian: "Kecerdasan buatan sedang mengubah teknologi",
      topic: "AI transformation"
    },
    {
      english: "Machine learning algorithms analyze data patterns",
      indonesian: "Algoritma machine learning menganalisis pola data", 
      topic: "ML data analysis"
    }
  ];
  
  for (const pair of languagePairs) {
    console.log(`\nTesting cross-language similarity:`);
    console.log(`Topic: ${pair.topic}`);
    console.log(`English: "${pair.english}"`);
    console.log(`Indonesian: "${pair.indonesian}"`);
    
    const englishEmbedding = await getEmbedding(pair.english, config.OPENAI_API_KEY);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const indonesianEmbedding = await getEmbedding(pair.indonesian, config.OPENAI_API_KEY);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const crossLangSimilarity = cosineSimilarity(englishEmbedding, indonesianEmbedding);
    
    console.log(`📊 Cross-language similarity: ${crossLangSimilarity.toFixed(4)}`);
    
    if (crossLangSimilarity > 0.6) {
      console.log('✅ Good cross-language understanding');
    } else if (crossLangSimilarity > 0.4) {
      console.log('⚠️  Moderate cross-language understanding');
    } else {
      console.log('❌ Poor cross-language understanding');
    }
  }
}

// Test 5: Performance and rate limiting
async function testPerformance() {
  console.log('\n🧪 Test 5: Performance Testing');
  console.log('==============================');
  
  const testTexts = [
    "Short text",
    "Medium length text with several words to test performance characteristics",
    "This is a longer text that contains multiple sentences and various topics to evaluate how well the embedding generation performs with increased content length and complexity, including technical terms and detailed descriptions."
  ];
  
  const results = [];
  
  for (let i = 0; i < testTexts.length; i++) {
    const text = testTexts[i];
    console.log(`\nPerformance test ${i + 1}:`);
    console.log(`Text length: ${text.length} characters`);
    console.log(`Word count: ${text.split(' ').length} words`);
    
    const startTime = Date.now();
    
    try {
      const embedding = await getEmbedding(text, config.OPENAI_API_KEY);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const performance = {
        textLength: text.length,
        wordCount: text.split(' ').length,
        duration: duration,
        charsPerSecond: (text.length / duration * 1000).toFixed(2),
        wordsPerSecond: (text.split(' ').length / duration * 1000).toFixed(2)
      };
      
      console.log(`✅ Performance metrics:`);
      console.log(`   - Duration: ${duration}ms`);
      console.log(`   - Speed: ${performance.charsPerSecond} chars/sec`);
      console.log(`   - Speed: ${performance.wordsPerSecond} words/sec`);
      
      results.push(performance);
      
    } catch (error) {
      console.error(`❌ Performance test failed: ${error.message}`);
    }
    
    // Rate limiting delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 Performance Summary:`);
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  console.log(`   - Average duration: ${avgDuration.toFixed(0)}ms`);
  console.log(`   - Tests completed: ${results.length}/${testTexts.length}`);
  
  return results;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Real Embedding API Tests');
  console.log('====================================');
  
  // Check API key
  if (!config.OPENAI_API_KEY) {
    console.error('❌ OPENAI_API_KEY environment variable not found!');
    console.log('💡 Run with: OPENAI_API_KEY=your_key node test-embedding-real.js');
    process.exit(1);
  }
  
  console.log('✅ OpenAI API key found');
  console.log(`📋 Configuration:`);
  console.log(`   - Model: ${config.MODEL}`);
  console.log(`   - Expected dimensions: ${config.EXPECTED_DIMENSIONS}`);
  
  try {
    const results = {};
    
    results.basic = await testBasicEmbedding();
    results.similarity = await testSimilarity();
    results.consistency = await testConsistency();
    results.crossLanguage = await testCrossLanguage();
    results.performance = await testPerformance();
    
    console.log('\n🎉 All Real API Tests Completed Successfully!');
    console.log('============================================');
    console.log('📊 Final Summary:');
    console.log(`   ✅ Basic embeddings: ${results.basic.length} generated`);
    console.log(`   ✅ Similarity tests: Multiple pairs tested`);
    console.log(`   ✅ Consistency: ${results.consistency.similarity.toFixed(4)} similarity`);
    console.log(`   ✅ Cross-language: Multiple language pairs`);
    console.log(`   ✅ Performance: ${results.performance.length} speed tests`);
    
    const totalApiCalls = results.basic.length + 6 + 2 + 4 + results.performance.length;
    console.log(`   📈 Total API calls made: ~${totalApiCalls}`);
    
    return results;
    
  } catch (error) {
    console.error('\n💥 Real API tests failed:', error.message);
    console.error('Stack trace:', error.stack);
    
    if (error.message.includes('API key')) {
      console.log('\n💡 API Key Issues:');
      console.log('1. Ensure OPENAI_API_KEY is set correctly');
      console.log('2. Verify the API key has sufficient credits');
      console.log('3. Check if the API key has embedding permissions');
    }
    
    if (error.message.includes('rate limit')) {
      console.log('\n💡 Rate Limiting:');
      console.log('1. The script includes delays between requests');
      console.log('2. Consider using a higher tier API key');
      console.log('3. Run tests with fewer iterations');
    }
    
    process.exit(1);
  }
}

// Run tests if executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testBasicEmbedding,
  testSimilarity,
  testConsistency,
  testCrossLanguage,
  testPerformance,
  getEmbedding,
  cosineSimilarity,
  config
};
