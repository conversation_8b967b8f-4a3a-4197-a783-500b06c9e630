// Test script untuk memverifikasi fix /context endpoint
const BASE_URL = 'https://ai.klikbuy.co.id';

async function makeRequest(endpoint, method = 'GET', body = null) {
  const url = `${BASE_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const responseText = await response.text();
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      ok: response.ok
    };
    
  } catch (error) {
    console.error(`❌ Request failed: ${error.message}`);
    throw error;
  }
}

async function testContextFix() {
  console.log('🧪 Testing /context Endpoint Fix');
  console.log('================================');
  console.log('⚠️  Note: This test requires the worker to be redeployed with the fix');
  
  // Step 1: Prepare test data
  console.log('\n📥 Step 1: Adding test data...');
  
  const testSessionId = `test-fix-${Date.now()}`;
  const testData = {
    text: "This is test content for context retrieval after the fix",
    session_id: testSessionId,
    agent: "test-fix-agent"
  };
  
  try {
    const ingestResult = await makeRequest('/ingest', 'POST', testData);
    
    if (ingestResult.ok) {
      console.log(`✅ Test data ingested: ${ingestResult.data.id}`);
    } else {
      console.log(`⚠️  Failed to ingest test data: ${ingestResult.data}`);
      return;
    }
  } catch (error) {
    console.log(`❌ Ingest failed: ${error.message}`);
    return;
  }
  
  // Wait for indexing
  console.log('\n⏳ Waiting 3 seconds for data indexing...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Step 2: Test context endpoint with various scenarios
  console.log('\n🔍 Step 2: Testing /context endpoint scenarios...');
  
  // Test case 1: Valid request
  console.log('\n📝 Test Case 1: Valid request');
  try {
    const listResult = await makeRequest('/list-system-prompts');
    if (listResult.ok && listResult.data.keys.length > 0) {
      const testPrompt = listResult.data.keys[0];
      console.log(`Using system prompt: ${testPrompt}`);
      
      const contextResult = await makeRequest('/context', 'POST', {
        system_prompt: testPrompt,
        session_id: testSessionId
      });
      
      if (contextResult.ok) {
        console.log(`✅ Context endpoint working!`);
        console.log(`   Context length: ${contextResult.data.context?.length || 0}`);
        console.log(`   Matches count: ${contextResult.data.matches_count || 0}`);
        
        if (contextResult.data.context) {
          console.log(`   Context preview: "${contextResult.data.context.substring(0, 100)}..."`);
        }
      } else {
        console.log(`❌ Context endpoint still failing: ${contextResult.status}`);
        console.log(`   Error: ${JSON.stringify(contextResult.data, null, 2)}`);
      }
    }
  } catch (error) {
    console.log(`❌ Valid request test failed: ${error.message}`);
  }
  
  // Test case 2: Missing fields
  console.log('\n📝 Test Case 2: Missing required fields');
  try {
    const contextResult = await makeRequest('/context', 'POST', {
      system_prompt: "test-prompt"
      // Missing session_id
    });
    
    if (contextResult.status === 400) {
      console.log(`✅ Correctly rejected missing fields (400)`);
      console.log(`   Error: ${contextResult.data.error}`);
    } else {
      console.log(`⚠️  Unexpected response for missing fields: ${contextResult.status}`);
    }
  } catch (error) {
    console.log(`❌ Missing fields test failed: ${error.message}`);
  }
  
  // Test case 3: Non-existent system prompt
  console.log('\n📝 Test Case 3: Non-existent system prompt');
  try {
    const contextResult = await makeRequest('/context', 'POST', {
      system_prompt: "non-existent-prompt-12345",
      session_id: testSessionId
    });
    
    if (contextResult.status === 404) {
      console.log(`✅ Correctly handled missing prompt (404)`);
      console.log(`   Error: ${contextResult.data.error}`);
    } else {
      console.log(`⚠️  Unexpected response for missing prompt: ${contextResult.status}`);
    }
  } catch (error) {
    console.log(`❌ Missing prompt test failed: ${error.message}`);
  }
  
  // Test case 4: Long system prompt (should be truncated)
  console.log('\n📝 Test Case 4: Long system prompt handling');
  try {
    const listResult = await makeRequest('/list-system-prompts');
    if (listResult.ok && listResult.data.keys.length > 0) {
      // Find a long prompt
      let longPrompt = null;
      for (const key of listResult.data.keys) {
        const getResult = await makeRequest(`/get-system-prompt?agent=${key}`);
        if (getResult.ok && getResult.data.prompt.length > 2000) {
          longPrompt = key;
          console.log(`Found long prompt: ${key} (${getResult.data.prompt.length} chars)`);
          break;
        }
      }
      
      if (longPrompt) {
        const contextResult = await makeRequest('/context', 'POST', {
          system_prompt: longPrompt,
          session_id: testSessionId
        });
        
        if (contextResult.ok) {
          console.log(`✅ Long prompt handled successfully!`);
          console.log(`   Context length: ${contextResult.data.context?.length || 0}`);
        } else {
          console.log(`❌ Long prompt still failing: ${contextResult.status}`);
          console.log(`   Error: ${JSON.stringify(contextResult.data, null, 2)}`);
        }
      } else {
        console.log(`ℹ️  No long prompts found to test`);
      }
    }
  } catch (error) {
    console.log(`❌ Long prompt test failed: ${error.message}`);
  }
}

async function compareBeforeAfter() {
  console.log('\n📊 Before vs After Comparison');
  console.log('=============================');
  
  console.log('❌ BEFORE (Original Issues):');
  console.log('   - 500 Internal Server Error');
  console.log('   - No error details returned');
  console.log('   - HTML error page instead of JSON');
  console.log('   - No handling for long prompts');
  console.log('   - No input validation');
  
  console.log('\n✅ AFTER (With Fix):');
  console.log('   - Proper error handling with try-catch');
  console.log('   - JSON error responses with details');
  console.log('   - Input validation for required fields');
  console.log('   - Long prompt truncation (>2000 chars)');
  console.log('   - Detailed logging for debugging');
  console.log('   - Proper HTTP status codes');
  console.log('   - Additional response metadata');
}

async function deploymentInstructions() {
  console.log('\n🚀 Deployment Instructions');
  console.log('==========================');
  
  console.log('To apply the fix, run these commands:');
  console.log('');
  console.log('1. Navigate to producer directory:');
  console.log('   cd producer');
  console.log('');
  console.log('2. Deploy the updated worker:');
  console.log('   wrangler deploy');
  console.log('');
  console.log('3. Wait 1-2 minutes for deployment to propagate');
  console.log('');
  console.log('4. Test the fix:');
  console.log('   node test-context-fix.js');
  console.log('');
  console.log('5. Monitor worker logs:');
  console.log('   wrangler tail');
}

async function runTest() {
  console.log('🔧 Context Endpoint Fix Verification');
  console.log('====================================');
  
  await testContextFix();
  await compareBeforeAfter();
  await deploymentInstructions();
  
  console.log('\n🎯 Summary');
  console.log('==========');
  console.log('✅ Fix implemented: Enhanced error handling');
  console.log('✅ Fix implemented: Input validation');
  console.log('✅ Fix implemented: Long prompt truncation');
  console.log('✅ Fix implemented: Proper JSON responses');
  console.log('✅ Fix implemented: Detailed logging');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy the updated worker');
  console.log('2. Run this test again to verify');
  console.log('3. Monitor worker logs for any remaining issues');
  console.log('4. Test with production data');
}

if (require.main === module) {
  runTest();
}

module.exports = { runTest, testContextFix, compareBeforeAfter, deploymentInstructions };
