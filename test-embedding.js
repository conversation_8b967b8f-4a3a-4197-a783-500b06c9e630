// Test script untuk fungsi embedding
// Jalankan dengan: node test-embedding.js

import { getEmbedding, storeVectorDoc, getAllVectorDocs, retrieveRelevantDocs } from './producer/utils/retrieval.js';

// Mock environment untuk testing
const mockEnv = {
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'your-openai-api-key-here',
  VECTOR_KV: {
    data: new Map(),
    async put(key, value) {
      this.data.set(key, value);
      console.log(`✅ Stored: ${key}`);
    },
    async get(key) {
      return this.data.get(key) || null;
    },
    async list(options = {}) {
      const keys = Array.from(this.data.keys())
        .filter(key => !options.prefix || key.startsWith(options.prefix))
        .map(name => ({ name }));
      return { keys };
    }
  },
  VECTORIZE: {
    vectors: [],
    async upsert(vectors) {
      this.vectors.push(...vectors);
      console.log(`✅ Upserted ${vectors.length} vectors to Vectorize`);
      return { count: vectors.length };
    },
    async query(options) {
      // Simple mock query - return all vectors with random scores
      const matches = this.vectors.map((vector, index) => ({
        id: vector.id,
        score: Math.random() * 0.5 + 0.5, // Random score between 0.5-1.0
        values: vector.values,
        metadata: vector.metadata
      }));
      
      // Sort by score and return topK
      matches.sort((a, b) => b.score - a.score);
      return { 
        matches: matches.slice(0, options.topK || 5)
      };
    }
  }
};

// Test functions
async function testGetEmbedding() {
  console.log('\n🧪 Testing getEmbedding function...');
  
  try {
    const testText = "This is a test sentence for embedding.";
    console.log(`Input text: "${testText}"`);
    
    const embedding = await getEmbedding(testText, mockEnv);
    
    console.log(`✅ Embedding generated successfully!`);
    console.log(`   - Type: ${typeof embedding}`);
    console.log(`   - Is Array: ${Array.isArray(embedding)}`);
    console.log(`   - Length: ${embedding.length}`);
    console.log(`   - First 5 values: [${embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    
    // Validate embedding
    if (!Array.isArray(embedding)) {
      throw new Error('Embedding should be an array');
    }
    
    if (embedding.length !== 1536) {
      console.log(`⚠️  Warning: Expected 1536 dimensions, got ${embedding.length}`);
    }
    
    if (embedding.some(val => typeof val !== 'number')) {
      throw new Error('All embedding values should be numbers');
    }
    
    console.log('✅ Embedding validation passed!');
    return embedding;
    
  } catch (error) {
    console.error('❌ Error testing getEmbedding:', error.message);
    throw error;
  }
}

async function testStoreAndRetrieve() {
  console.log('\n🧪 Testing store and retrieve functions...');
  
  try {
    // Test data
    const testDocs = [
      {
        id: 'doc1',
        content: 'Machine learning is a subset of artificial intelligence.',
        meta: { category: 'AI', source: 'test' }
      },
      {
        id: 'doc2', 
        content: 'Deep learning uses neural networks with multiple layers.',
        meta: { category: 'AI', source: 'test' }
      },
      {
        id: 'doc3',
        content: 'Cooking pasta requires boiling water and salt.',
        meta: { category: 'cooking', source: 'test' }
      }
    ];
    
    // Generate embeddings and store documents
    console.log('Generating embeddings and storing documents...');
    for (const doc of testDocs) {
      const embedding = await getEmbedding(doc.content, mockEnv);
      const vectorDoc = {
        ...doc,
        embedding
      };
      await storeVectorDoc(vectorDoc, mockEnv);
    }
    
    // Test retrieval
    console.log('\nTesting document retrieval...');
    const allDocs = await getAllVectorDocs(mockEnv);
    console.log(`✅ Retrieved ${allDocs.length} documents`);
    
    // Test similarity search
    console.log('\nTesting similarity search...');
    const query = 'What is artificial intelligence?';
    console.log(`Query: "${query}"`);
    
    const relevantDocs = await retrieveRelevantDocs(query, mockEnv, 2);
    console.log(`✅ Found ${relevantDocs.length} relevant documents:`);
    
    relevantDocs.forEach((doc, index) => {
      console.log(`   ${index + 1}. [Score: ${doc.score?.toFixed(4)}] ${doc.content.substring(0, 50)}...`);
    });
    
  } catch (error) {
    console.error('❌ Error testing store and retrieve:', error.message);
    throw error;
  }
}

async function testEmbeddingEndpoints() {
  console.log('\n🧪 Testing embedding endpoints...');
  
  try {
    // Test /ingest endpoint
    console.log('Testing /ingest endpoint...');
    const ingestData = {
      text: 'This is test content for ingestion.',
      session_id: 'test-session-123',
      agent: 'test-agent'
    };
    
    // Simulate the /ingest endpoint logic
    const embedding = await getEmbedding(ingestData.text, mockEnv);
    const vectorId = crypto.randomUUID();
    
    await mockEnv.VECTORIZE.upsert([{
      id: vectorId,
      values: embedding,
      metadata: {
        session_id: ingestData.session_id,
        agent: ingestData.agent
      }
    }]);
    
    console.log(`✅ Ingest successful! Vector ID: ${vectorId}`);
    
    // Test /context endpoint
    console.log('\nTesting /context endpoint...');
    const contextQuery = 'test content';
    const queryEmbedding = await getEmbedding(contextQuery, mockEnv);
    
    const results = await mockEnv.VECTORIZE.query({
      topK: 5,
      vector: queryEmbedding,
      filter: { session_id: 'test-session-123' },
      returnMetadata: true
    });
    
    console.log(`✅ Context search successful! Found ${results.matches.length} matches`);
    results.matches.forEach((match, index) => {
      console.log(`   ${index + 1}. Score: ${match.score.toFixed(4)}, Agent: ${match.metadata?.agent}`);
    });
    
  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
    throw error;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Embedding Tests...');
  console.log('=====================================');
  
  try {
    // Check if OpenAI API key is available
    if (!mockEnv.OPENAI_API_KEY || mockEnv.OPENAI_API_KEY === 'your-openai-api-key-here') {
      console.log('⚠️  Warning: No OpenAI API key found. Set OPENAI_API_KEY environment variable.');
      console.log('   Tests will run but may fail on actual API calls.');
    }
    
    await testGetEmbedding();
    await testStoreAndRetrieve();
    await testEmbeddingEndpoints();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('=====================================');
    
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    console.error('=====================================');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}

export { runAllTests, testGetEmbedding, testStoreAndRetrieve, testEmbeddingEndpoints };
