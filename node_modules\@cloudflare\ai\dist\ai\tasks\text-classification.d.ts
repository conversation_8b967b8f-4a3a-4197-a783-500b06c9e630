import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiTextClassificationInput, BaseAiTextClassification } from "./types/tasks";
export declare class AiTextClassification extends BaseAiTextClassification implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                text: {
                    type: string;
                    minLength: number;
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            items: {
                type: string;
                properties: {
                    score: {
                        type: string;
                    };
                    label: {
                        type: string;
                    };
                };
            };
        };
    };
    constructor(inputs: AiTextClassificationInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=text-classification.d.ts.map