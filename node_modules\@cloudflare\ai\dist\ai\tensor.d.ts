export declare enum TensorType {
    String = "str",
    Bool = "bool",
    Float16 = "float16",
    Float32 = "float32",
    Int16 = "int16",
    Int32 = "int32",
    Int64 = "int64",
    Int8 = "int8",
    Uint16 = "uint16",
    Uint32 = "uint32",
    Uint64 = "uint64",
    Uint8 = "uint8"
}
type TensorOpts = {
    shape?: number[];
    name?: string;
    validate?: boolean;
};
export type TensorsObject = {
    [name: string]: Tensor<any>;
};
export declare class Tensor<T extends TensorType> {
    type: T;
    value: any | any[];
    name: string | null;
    shape: number[];
    constructor(type: T, value: any | any[], opts?: TensorOpts);
    static fromJSON(obj: any): Tensor<any>;
    toJSON(): {
        type: T;
        shape: number[];
        name: string;
        value: any;
    };
}
export {};
//# sourceMappingURL=tensor.d.ts.map