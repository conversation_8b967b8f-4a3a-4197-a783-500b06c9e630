import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiSpeechRecognitionInput, BaseAiSpeechRecognition } from "./types/tasks";
export declare class AiSpeechRecognition extends BaseAiSpeechRecognition implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            oneOf: ({
                type: string;
                format: string;
                properties?: undefined;
                required?: undefined;
            } | {
                type: string;
                properties: {
                    audio: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                };
                required: string[];
                format?: undefined;
            })[];
        };
        output: {
            type: string;
            contentType: string;
            properties: {
                text: {
                    type: string;
                };
                word_count: {
                    type: string;
                };
                words: {
                    type: string;
                    items: {
                        type: string;
                        properties: {
                            word: {
                                type: string;
                            };
                            start: {
                                type: string;
                            };
                            end: {
                                type: string;
                            };
                        };
                    };
                };
                vtt: {
                    type: string;
                };
            };
            required: string[];
        };
    };
    constructor(inputs: AiSpeechRecognitionInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=speech-recognition.d.ts.map