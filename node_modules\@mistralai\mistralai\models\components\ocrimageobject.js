"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRImageObject$ = exports.OCRImageObject$outboundSchema = exports.OCRImageObject$inboundSchema = void 0;
exports.ocrImageObjectToJSON = ocrImageObjectToJSON;
exports.ocrImageObjectFromJSON = ocrImageObjectFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.OCRImageObject$inboundSchema = z.object({
    id: z.string(),
    top_left_x: z.nullable(z.number().int()),
    top_left_y: z.nullable(z.number().int()),
    bottom_right_x: z.nullable(z.number().int()),
    bottom_right_y: z.nullable(z.number().int()),
    image_base64: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "top_left_x": "topLeftX",
        "top_left_y": "topLeftY",
        "bottom_right_x": "bottomRightX",
        "bottom_right_y": "bottomRightY",
        "image_base64": "imageBase64",
    });
});
/** @internal */
exports.OCRImageObject$outboundSchema = z.object({
    id: z.string(),
    topLeftX: z.nullable(z.number().int()),
    topLeftY: z.nullable(z.number().int()),
    bottomRightX: z.nullable(z.number().int()),
    bottomRightY: z.nullable(z.number().int()),
    imageBase64: z.nullable(z.string()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        topLeftX: "top_left_x",
        topLeftY: "top_left_y",
        bottomRightX: "bottom_right_x",
        bottomRightY: "bottom_right_y",
        imageBase64: "image_base64",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRImageObject$;
(function (OCRImageObject$) {
    /** @deprecated use `OCRImageObject$inboundSchema` instead. */
    OCRImageObject$.inboundSchema = exports.OCRImageObject$inboundSchema;
    /** @deprecated use `OCRImageObject$outboundSchema` instead. */
    OCRImageObject$.outboundSchema = exports.OCRImageObject$outboundSchema;
})(OCRImageObject$ || (exports.OCRImageObject$ = OCRImageObject$ = {}));
function ocrImageObjectToJSON(ocrImageObject) {
    return JSON.stringify(exports.OCRImageObject$outboundSchema.parse(ocrImageObject));
}
function ocrImageObjectFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRImageObject$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRImageObject' from JSON`);
}
//# sourceMappingURL=ocrimageobject.js.map