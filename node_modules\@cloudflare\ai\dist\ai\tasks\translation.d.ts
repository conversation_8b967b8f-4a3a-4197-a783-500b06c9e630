import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiTranslationInput, BaseAiTranslation } from "./types/tasks";
export declare class AiTranslation extends BaseAiTranslation implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                text: {
                    type: string;
                    minLength: number;
                };
                source_lang: {
                    type: string;
                    default: string;
                };
                target_lang: {
                    type: string;
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            properties: {
                translated_text: {
                    type: string;
                };
            };
        };
    };
    constructor(inputs: AiTranslationInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=translation.d.ts.map