import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiImageClassificationInput, BaseAiImageClassification } from "./types/tasks";
export declare class AiImageClassification extends BaseAiImageClassification implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            oneOf: ({
                type: string;
                format: string;
                properties?: undefined;
                required?: undefined;
            } | {
                type: string;
                properties: {
                    image: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                };
                required: string[];
                format?: undefined;
            })[];
        };
        output: {
            type: string;
            contentType: string;
            items: {
                type: string;
                properties: {
                    score: {
                        type: string;
                    };
                    label: {
                        type: string;
                    };
                };
            };
        };
    };
    constructor(inputs: AiImageClassificationInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=image-classification.d.ts.map