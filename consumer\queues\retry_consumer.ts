import taskWorkflow from "producer/workflow/task_workflow"
import { generateImagesFromBrochures } from "producer/handlers/generate_images_from_brochures"

export default {
  async queue(batch: any, env: any) {
    if (!env.TASK_STATUS) {
      console.error('TASK_STATUS KV binding is missing from env:', env);
      return;
    }
    if (!batch || !batch.messages || !Array.isArray(batch.messages)) {
      console.error('Queue batch missing messages array:', JSON.stringify(batch));
      return;
    }
    for (const message of batch.messages) {
      if (!message.body) {
        console.error('Queue message missing body:', JSON.stringify(message));
        await env.TASK_STATUS.put('status:unknown', JSON.stringify({ status: 'failed', error: 'Message body is undefined' }));
        continue;
      }
      const { task_id, payload, project_key, action, last_success_agent } = message.body;
      console.log(`Processing RETRY task: ${task_id} (resume from: ${last_success_agent || 'start'})`);
      let status = "processing";
      let loadtime: number | null = null;
      let result = null;
      try {
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "processing" }));
        const startTime = Date.now();
        if(action === "generate_brochure_images"){
          result = await taskWorkflow.runWorkflow(payload, env, task_id, last_success_agent);
        }else if(action === "generate_banner_images"){
          result = await taskWorkflow.runWorkflow_banner(payload, env, task_id);
        }
        const loadtimeMs = Date.now() - startTime;
        loadtime = parseFloat((loadtimeMs / 60000).toFixed(2)); // menit, dua desimal
        await env.TASK_STATUS.put(`result:${task_id}`, JSON.stringify(result));
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "completed", loadtime: loadtime+' minutes' }));
        status = "completed";
      } catch (e: any) {
        console.error(`Error RETRY task ${task_id}`, e);
        await env.TASK_STATUS.put(`status:${task_id}`, JSON.stringify({ status: "failed", error: e.message }));
        status = "failed";
        // Optional: requeue again if needed, or add retry count logic here
      }
      // Kirim status ke webhook eksternal
      try {
        await fetch("https://ai.gass.co.id/webhook.html", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            act: action,
            status: status,
            task_id,
            project_key,
            loadtime: loadtime
          })
        });
      } catch (webhookErr) {
        console.error("Failed to send webhook notification", webhookErr);
      }
    }
  }
} 