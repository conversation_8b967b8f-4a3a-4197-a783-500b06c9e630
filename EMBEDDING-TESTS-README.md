# Embedding Functionality Tests

This directory contains comprehensive test scripts to validate the embedding functionality in your Cloudflare Worker application.

## 📁 Test Files

### 1. `test-embedding-mock.js`
**Mock tests that don't require API keys**
- Tests `getEmbedding` function with mock OpenAI client
- Validates embedding generation, storage, and retrieval logic
- Simulates `/ingest` and `/context` endpoint behavior
- Tests error handling scenarios

```bash
node test-embedding-mock.js
```

### 2. `test-embedding-real.js`
**Real API tests using OpenAI's embedding service**
- Tests actual OpenAI text-embedding-ada-002 model
- Validates embedding consistency and similarity
- Tests cross-language capabilities (English/Indonesian)
- Performance benchmarking

```bash
OPENAI_API_KEY=your_key node test-embedding-real.js
```

### 3. `test-worker-endpoints.js`
**Tests deployed Cloudflare Worker endpoints**
- Tests `/ingest` endpoint with real Vectorize database
- Tests `/context` endpoint with KV store integration
- Tests system prompt management endpoints
- Validates error handling and response formats

```bash
BASE_URL=https://your-worker.workers.dev node test-worker-endpoints.js
```

### 4. `run-all-tests.js`
**Comprehensive test runner**
- Runs all test suites with proper environment checking
- Provides detailed summary and recommendations
- Supports running individual test types

```bash
node run-all-tests.js [test-type]
```

## 🚀 Quick Start

### 1. Run Mock Tests (No Setup Required)
```bash
node test-embedding-mock.js
```

### 2. Run Real API Tests
```bash
# Set your OpenAI API key
export OPENAI_API_KEY=sk-your-openai-api-key-here

# Run the tests
node test-embedding-real.js
```

### 3. Run Worker Endpoint Tests
```bash
# Set your deployed worker URL
export BASE_URL=https://your-worker.your-subdomain.workers.dev

# Run the tests
node test-worker-endpoints.js
```

### 4. Run All Tests
```bash
# Set environment variables
export OPENAI_API_KEY=sk-your-openai-api-key-here
export BASE_URL=https://your-worker.your-subdomain.workers.dev

# Run all tests
node run-all-tests.js
```

## 📋 Test Coverage

### Core Functions Tested
- ✅ `getEmbedding()` - OpenAI embedding generation
- ✅ Vector storage in Vectorize database
- ✅ Similarity search and retrieval
- ✅ System prompt management in KV store
- ✅ Error handling and validation

### API Endpoints Tested
- ✅ `POST /ingest` - Store text embeddings
- ✅ `POST /context` - Retrieve similar embeddings
- ✅ `POST /add-system-prompt` - Add system prompts
- ✅ `GET /get-system-prompt` - Retrieve system prompts
- ✅ `GET /list-system-prompts` - List all prompts

### Validation Checks
- ✅ Embedding dimensions (1536 for text-embedding-ada-002)
- ✅ Data types and value ranges
- ✅ Response format consistency
- ✅ Error status codes
- ✅ Performance metrics

## 🔧 Environment Setup

### Required Dependencies
```bash
npm install openai uuid
```

### Environment Variables
```bash
# For real API tests
export OPENAI_API_KEY=sk-your-openai-api-key-here

# For worker endpoint tests  
export BASE_URL=https://your-worker.your-subdomain.workers.dev

# Optional: Custom test session ID
export TEST_SESSION_ID=my-custom-session
```

### Worker Configuration
Ensure your Cloudflare Worker has:
- ✅ OPENAI_API_KEY environment variable set
- ✅ Vectorize database bound as `VECTORIZE`
- ✅ KV namespace bound as `PROMPT`
- ✅ Proper CORS headers if testing from browser

## 📊 Expected Results

### Mock Tests
```
🚀 Starting Mock Embedding Tests
================================

🧪 Test 1: getEmbedding Function
================================
✅ Generated embedding:
   - Type: object
   - Is Array: true
   - Length: 1536
   - Sample: [0.1234, -0.5678, 0.9012...]

🎉 All Mock Tests Completed Successfully!
```

### Real API Tests
```
🚀 Starting Real Embedding API Tests
====================================
✅ OpenAI API key found

🧪 Test 1: Basic Embedding Generation
====================================
🔄 Generating embedding for: "Machine learning is a subset..."
✅ Embedding generated successfully (1536 dimensions)
📊 Validation Results:
   - Dimensions: 1536
   - Generation time: 1234ms
   - Value range: -0.123456 to 0.987654

🎉 All Real API Tests Completed Successfully!
```

### Worker Endpoint Tests
```
🚀 Starting Cloudflare Worker Endpoint Tests
============================================

🧪 Test 1: /ingest Endpoint
===========================
🔄 POST /ingest
✅ Ingest successful in 2345ms
   Vector ID: 12345678-1234-1234-1234-123456789abc
   Status: ok

🎉 All Worker Endpoint Tests Completed!
```

## 🐛 Troubleshooting

### Common Issues

**1. "OpenAI API key is required"**
```bash
# Check if API key is set
echo $OPENAI_API_KEY

# Set the API key
export OPENAI_API_KEY=sk-your-key-here
```

**2. "Worker not accessible"**
```bash
# Verify worker URL
curl https://your-worker.workers.dev/

# Check deployment status
wrangler deployments list
```

**3. "Embedding failed or wrong dimension"**
- Check OpenAI API key validity
- Verify API quota/billing
- Check network connectivity

**4. "System prompt not found in KV"**
- Ensure KV namespace is bound to worker
- Add test prompts using `/add-system-prompt`
- Check KV store contents in dashboard

### Debug Mode
Add debug logging to any test:
```bash
DEBUG=true node test-embedding-real.js
```

## 📈 Performance Benchmarks

### Expected Performance
- **Embedding generation**: 1-3 seconds per request
- **Vector storage**: < 500ms
- **Similarity search**: < 1 second
- **Cross-language similarity**: 0.6-0.8 for related content

### Rate Limits
- OpenAI API: 3,000 requests/minute (varies by tier)
- Cloudflare Workers: 100,000 requests/day (free tier)
- Tests include appropriate delays to avoid limits

## 🔄 Continuous Testing

### Automated Testing
```bash
# Run tests every hour
0 * * * * cd /path/to/project && node run-all-tests.js mock

# Run full tests daily
0 2 * * * cd /path/to/project && node run-all-tests.js
```

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Run Embedding Tests
  run: |
    export OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
    export BASE_URL=${{ secrets.WORKER_URL }}
    node run-all-tests.js
```

## 📝 Adding New Tests

To add new test cases:

1. **For mock tests**: Add to `test-embedding-mock.js`
2. **For API tests**: Add to `test-embedding-real.js`  
3. **For endpoints**: Add to `test-worker-endpoints.js`

Example new test function:
```javascript
async function testNewFeature() {
  console.log('\n🧪 Test: New Feature');
  console.log('====================');
  
  try {
    // Test implementation
    console.log('✅ Test passed');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}
```

## 📞 Support

If tests fail consistently:
1. Check the detailed error messages in test output
2. Verify all environment variables are set correctly
3. Ensure worker is deployed and accessible
4. Check Cloudflare dashboard for worker logs
5. Verify OpenAI API key has sufficient credits
