import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiObjectDetectionInput, BaseAiObjectDetection } from "./types/tasks";
export declare class AiObjectDetection extends BaseAiObjectDetection implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            oneOf: ({
                type: string;
                format: string;
                properties?: undefined;
            } | {
                type: string;
                properties: {
                    image: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                };
                format?: undefined;
            })[];
        };
        output: {
            type: string;
            contentType: string;
            items: {
                type: string;
                properties: {
                    score: {
                        type: string;
                    };
                    label: {
                        type: string;
                    };
                    box: {
                        type: string;
                        properties: {
                            xmin: {
                                type: string;
                            };
                            ymin: {
                                type: string;
                            };
                            xmax: {
                                type: string;
                            };
                            ymax: {
                                type: string;
                            };
                        };
                    };
                };
            };
        };
    };
    constructor(inputs: AiObjectDetectionInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=object-detection.d.ts.map