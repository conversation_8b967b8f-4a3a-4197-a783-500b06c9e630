import contextPlannerImageAgent from "./agent-backup/context_planner_image_generator"
import simplifiedBrochureAgent from "./agent-backup/simplified_digital_brochure_prompt_generator"
import productAudienceMatchingAgent from "./agent-backup/product_audience_matching"
import marketingAnalysisAgent from "./agent-backup/marketing_analysis"
import taskWorkflow from "./workflow/task_workflow"
import submitTask from "./handlers/submit_task"
import checkStatus from "./handlers/check_status"
import { generateImagesFromBrochures } from "./handlers/generate_images_from_brochures"
import ctaButtonMapper from "./agents/cta_button_mapping"
import imageResizer from "./handlers/image_resizer"
import agent from "./agents/agent"
import type { ExecutionContext } from '@cloudflare/workers-types'
import { Mistral } from '@mistralai/mistralai';
import websiteOcrR2 from "./agents/website_screenshot_ocr"
import agentRag from "./agents/agent_rag"
import { getEmbedding } from "./utils/retrieval"
import { v4 as uuidv4 } from 'uuid'

export default {
  async fetch(request: Request, env: any) {
    const url = new URL(request.url);

    if(url.pathname === "/ingest") {
      const { text, session_id, agent } = await request.json()
      const embedding = await getEmbedding(text, env)
      const vectorId = crypto.randomUUID()

      await env.VECTORIZE.upsert([{
        id: vectorId,
        values: embedding,
        metadata: {
          session_id,
          agent
        }
      }])

      return new Response(JSON.stringify({ status: 'ok', id: vectorId }), { status: 200 })
    }

    if(url.pathname === "/context") {
      try {
        const { system_prompt, session_id } = await request.json()

        if (!system_prompt || !session_id) {
          return new Response(JSON.stringify({
            error: "Missing required fields: system_prompt and session_id"
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }

        const systemPromptKV = await env.PROMPT.get(system_prompt)
        if (!systemPromptKV) {
          return new Response(JSON.stringify({
            error: "System prompt not found in KV",
            system_prompt: system_prompt
          }), {
            status: 404,
            headers: { "Content-Type": "application/json" }
          });
        }

        console.log("systemPromptKV length:", systemPromptKV.length);

        // Truncate very long prompts to avoid timeout
        const truncatedPrompt = systemPromptKV.length > 2000
          ? systemPromptKV.substring(0, 2000) + "..."
          : systemPromptKV;

        console.log("Generating embedding for prompt...");
        const embedding = await getEmbedding(truncatedPrompt, env)
        console.log("embedding generated, length:", embedding?.length);

        if (!embedding || !Array.isArray(embedding) || embedding.length !== 1536) {
          return new Response(JSON.stringify({
            error: "Embedding failed or wrong dimension",
            dimension: embedding ? embedding.length : "undefined"
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }

        console.log("Querying Vectorize...");
        const results = await env.VECTORIZE.query({
          topK: 5,
          vector: embedding,
          filter: { session_id },
          returnMetadata: true
        })

        console.log("Vectorize query results:", results.matches?.length || 0, "matches");
        const combinedContext = results.matches.map((match: any) => match.document || match.metadata?.text || '').join('\n\n')

        return new Response(JSON.stringify({
          context: combinedContext,
          matches_count: results.matches?.length || 0
        }), {
          status: 200,
          headers: { "Content-Type": "application/json" }
        })

      } catch (error: any) {
        console.error("Context endpoint error:", error);
        return new Response(JSON.stringify({
          error: "Internal server error",
          details: error.message || String(error),
          endpoint: "/context"
        }), {
          status: 500,
          headers: { "Content-Type": "application/json" }
        });
      }
    }

    if (url.pathname === "/agent/cta-button-mapping") {
      return ctaButtonMapper.fetch(request, env);
    }

    if (url.pathname === "/agent/context-planner-image") {
      return contextPlannerImageAgent.fetch(request, env);
    }

    if (url.pathname === "/agent/simplified-brochure") {
      return simplifiedBrochureAgent.fetch(request, env);
    }

    if (url.pathname === "/agent/marketing-analysis") {
      return marketingAnalysisAgent.fetch(request, env);
    }

    if (url.pathname === "/agent/product-audience-matching") {
      return productAudienceMatchingAgent.fetch(request, env);
    }

    if (url.pathname === "/workflow/marketing-brochure") {
       const result = await taskWorkflow.fetch(request, env);
        return result;
    }

    if (url.pathname === "/generate-images-from-brochures") {
      //console.log("generate-images-from-brochures")
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      //console.log("request", request)
      const input = await request.json()
      //console.log("input", input)
      const imagesResult = await generateImagesFromBrochures(input, env, "1")
      return new Response(JSON.stringify(imagesResult), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/submit-task") {
      return submitTask.fetch(request, env, "generate_brochure_images");
    }

    if (url.pathname === "/submit-task-banner") {
      return submitTask.fetch(request, env, "generate_banner_images");
    }

    if (url.pathname === "/check-status") {
      return checkStatus.fetch(request, env);
    }

    if (url.pathname === "/check-status-sse") {
      return checkStatus.stream(request, env);
    }

    if (url.pathname === "/resize-image") {
      return imageResizer.fetch(request, env);
    }

    if (url.pathname === "/audience-analysis-agentRag") {
      const input = await request.json();
      const result = await agentRag.fetch('prompt_audience_analysis', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/audience-analysis") {
      const input = await request.json();
      const result = await agent.fetch('prompt_audience_analysis', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/audience-profiling") {
      const input = await request.json();
      const result = await agent.fetch('prompt-audience-profiling', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/product-audience-matching") {
      const input = await request.json();
      const result = await agent.fetch('prompt-product-audience-matching', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/ads-copy-generation") {
      const input = await request.json();
      const result = await agent.fetch('prompt-ads-copy-generation', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/context-planner-generator") {
      const input = await request.json();
      const result = await agent.fetch('prompt-ad-banner-context-planner-generator', JSON.stringify(input, null, 2), env, "claude-3-7-sonnet-latest");
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/banner-prompt-generator") {
      const input = await request.json();
      const result = await agent.fetch('prompt-ad-banner-prompt-generator', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/banner-generator") {
      const input = await request.json();
      const result = await agent.fetch('prompt-banner-generator', JSON.stringify(input, null, 2), env);
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/ocr-text-extractor") {
      const input = await request.json();

      const visionResponse = await fetch(`https://vision.googleapis.com/v1/images:annotate?key=AIzaSyAkFsdk82LtSft-awwCV_n9y6L_MaCa7rI`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          requests: [
            {
              image: {
                source: {
                  imageUri: input.website_screenshot
                }
              },
              features: [
                { type: 'TEXT_DETECTION' }
              ]
            }
          ]
        })
      });

      const result = await visionResponse.json();

      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/website-ocr-r2") {
      return websiteOcrR2.fetch(request, env);
    }

    if (url.pathname === "/update-system-prompt") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      const contentType = request.headers.get("content-type") || "";
      if (!contentType.includes("application/x-www-form-urlencoded")) {
        return new Response("Unsupported Media Type", { status: 415 });
      }
      const formData = await request.text();
      const params = new URLSearchParams(formData);
      const agentKey = params.get("agent");
      const promptValue = params.get("prompt");
      if (!agentKey || !promptValue) {
        return new Response(JSON.stringify({ error: "Missing 'agent' or 'prompt' field" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      try {
        await env.PROMPT.put(agentKey, promptValue);
        return new Response(JSON.stringify({ status: "ok", agent: agentKey }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      } catch (e: any) {
        return new Response(JSON.stringify({ error: "Failed to update system prompt", details: e && e.message ? e.message : String(e) }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    if (url.pathname === "/get-system-prompt") {
      if (request.method !== "GET") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      const agentKey = url.searchParams.get("agent");
      if (!agentKey) {
        return new Response(JSON.stringify({ error: "Missing 'agent' query parameter" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      const promptValue = await env.PROMPT.get(agentKey);
      if (promptValue === null || promptValue === undefined) {
        return new Response(JSON.stringify({ error: "Prompt not found for agent" }), {
          status: 404,
          headers: { "Content-Type": "application/json" },
        });
      }
      return new Response(JSON.stringify({ agent: agentKey, prompt: promptValue }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/generate-banner-images") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      const input = await request.json();
      // Panggil generateImagesBanner dengan input yang diterima
      const result = await import("./handlers/generate_images_banner").then(mod => mod.generateImagesBanner(input, env, "1"));
      return new Response(JSON.stringify(result), {
        headers: { "Content-Type": "application/json" },
      });
    }

    if (url.pathname === "/test-vertex-image") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      const input = await request.json();
      const prompt = input.prompt;
      if (!prompt) {
        return new Response(JSON.stringify({ error: "Missing prompt" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      // Compose Vertex AI endpoint via Cloudflare Gateway
      const urlVertex = `https://gateway.ai.cloudflare.com/v1/${env.CF_ACCOUNT_ID}/rag-gass/google-vertex-ai/v1/projects/${env.GCP_PROJECT_ID}/locations/${env.GCP_REGION}/publishers/google/models/imagen-4.0-generate-preview-05-20:predict`;
      const body = {
        instances: [{ prompt }],
        parameters: { sampleCount: 1 }
      };
      const response = await fetch(urlVertex, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${env.VERTEX_API_KEY}`,
          "cf-aig-authorization": "Bearer "+env.CF_AI_GATEWAY_KEY,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(body)
      });
      const data = await response.json();
      // Return base64 image or error
      if (data && data.predictions && data.predictions[0] && data.predictions[0].bytesBase64Encoded) {
        return new Response(data.predictions[0].bytesBase64Encoded);
      } else {
        return new Response(JSON.stringify({ error: data.error || data }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    if (url.pathname === "/add-system-prompt") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      const contentType = request.headers.get("content-type") || "";
      if (!contentType.includes("application/x-www-form-urlencoded")) {
        return new Response("Unsupported Media Type", { status: 415 });
      }
      const formData = await request.text();
      const params = new URLSearchParams(formData);
      const agentKey = params.get("agent");
      const promptValue = params.get("prompt");
      if (!agentKey || !promptValue) {
        return new Response(JSON.stringify({ error: "Missing 'agent' or 'prompt' field" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      try {
        await env.PROMPT.put(agentKey, promptValue);
        return new Response(JSON.stringify({ status: "ok", agent: agentKey }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      } catch (e: any) {
        return new Response(JSON.stringify({ error: "Failed to add system prompt", details: e && e.message ? e.message : String(e) }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    if (url.pathname === "/list-system-prompts") {
      if (request.method !== "GET") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      try {
        const list = await env.PROMPT.list();
        const keys = list.keys.map((k: any) => k.name);
        return new Response(JSON.stringify({ keys }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      } catch (e: any) {
        return new Response(JSON.stringify({ error: "Failed to list system prompts", details: e && e.message ? e.message : String(e) }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    if (url.pathname === "/run-auto-workflow") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      let body;
      try {
        body = await request.json();
      } catch (e) {
        return new Response(JSON.stringify({ error: "Invalid JSON body" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      const { steps, input} = body;
      if (!Array.isArray(steps) || !input) {
        return new Response(JSON.stringify({ error: "Missing required fields: steps (array), input, task_id" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      try {
        const taskId = uuidv4()
        const result = await taskWorkflow.runAutoWorkflow(steps, input, env, taskId);
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      } catch (e: any) {
        return new Response(JSON.stringify({ error: e && e.message ? e.message : String(e) }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    if (url.pathname === "/run-auto-workflow-v2") {
      if (request.method !== "POST") {
        return new Response("Method Not Allowed", { status: 405 });
      }
      let body;
      try {
        body = await request.json();
      } catch (e) {
        return new Response(JSON.stringify({ error: "Invalid JSON body" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
      const { workflow_id, input} = body;
      try {
        const steps = JSON.parse(await env.WORKFLOW.get(workflow_id));
        const taskId = uuidv4()
        const result = await taskWorkflow.runAutoWorkflow(steps, input, env, taskId);
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      } catch (e: any) {
        return new Response(JSON.stringify({ error: e && e.message ? e.message : String(e) }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    return new Response("Not Found", { status: 404 });
  },

};
