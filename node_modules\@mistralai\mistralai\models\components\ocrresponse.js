"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRResponse$ = exports.OCRResponse$outboundSchema = exports.OCRResponse$inboundSchema = void 0;
exports.ocrResponseToJSON = ocrResponseToJSON;
exports.ocrResponseFromJSON = ocrResponseFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const ocrpageobject_js_1 = require("./ocrpageobject.js");
const ocrusageinfo_js_1 = require("./ocrusageinfo.js");
/** @internal */
exports.OCRResponse$inboundSchema = z.object({
    pages: z.array(ocrpageobject_js_1.OCRPageObject$inboundSchema),
    model: z.string(),
    usage_info: ocrusageinfo_js_1.OCRUsageInfo$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "usage_info": "usageInfo",
    });
});
/** @internal */
exports.OCRResponse$outboundSchema = z.object({
    pages: z.array(ocrpageobject_js_1.OCRPageObject$outboundSchema),
    model: z.string(),
    usageInfo: ocrusageinfo_js_1.OCRUsageInfo$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        usageInfo: "usage_info",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRResponse$;
(function (OCRResponse$) {
    /** @deprecated use `OCRResponse$inboundSchema` instead. */
    OCRResponse$.inboundSchema = exports.OCRResponse$inboundSchema;
    /** @deprecated use `OCRResponse$outboundSchema` instead. */
    OCRResponse$.outboundSchema = exports.OCRResponse$outboundSchema;
})(OCRResponse$ || (exports.OCRResponse$ = OCRResponse$ = {}));
function ocrResponseToJSON(ocrResponse) {
    return JSON.stringify(exports.OCRResponse$outboundSchema.parse(ocrResponse));
}
function ocrResponseFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRResponse$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRResponse' from JSON`);
}
//# sourceMappingURL=ocrresponse.js.map