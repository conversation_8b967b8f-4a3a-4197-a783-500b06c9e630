import { TensorsObject } from "./tensor";
export declare const generatePrompt: (inputs: any, args: any) => any;
export declare const parseInputs: (inputs: any) => any[];
export declare const tensorByName: (result: any) => TensorsObject;
export declare const getModelSettings: (model: string, key?: string) => any;
export declare const setModelSettings: (model: string, settings: any) => void;
export interface EventSourceParser {
    feed(chunk: string): void;
    reset(): void;
}
export interface ParsedEvent {
    type: "event";
    event?: string;
    id?: string;
    data: string;
}
export interface ReconnectInterval {
    type: "reconnect-interval";
    value: number;
}
export type ParseEvent = ParsedEvent | ReconnectInterval;
export type EventSourceParseCallback = (event: ParseEvent) => void;
export declare class EventSourceParserStream extends TransformStream<string, ParsedEvent> {
    constructor();
}
export declare function createParser(onParse: EventSourceParseCallback): EventSourceParser;
export declare class ResultStream extends TransformStream {
    constructor();
}
export declare const getEventStream: (body: ReadableStream) => {
    readable: ReadableStream<any>;
    reader: ReadableStreamDefaultReader<any>;
    writer: WritableStreamDefaultWriter<any>;
    write: (data: string) => Promise<void>;
};
export declare const readStream: (body: ReadableStream, debug: boolean, ctx: any, pushLog: any, tensorData: boolean, postProcessing: CallableFunction | false) => ReadableStream<any>;
//# sourceMappingURL=tools.d.ts.map