import { Tensor, TensorType } from "../tensor";
export declare const defaultContexts: {
    chat: string;
    code: string;
    function: string;
};
export declare const defaultPromptMessages: (context: string, prompt: string) => {
    role: string;
    content: string;
}[];
export declare const vLLMGenerateTensors: (preProcessedInputs: any) => Tensor<TensorType.String>[];
export declare const tgiPostProc: (response: any, ignoreTokens?: any) => any;
export declare const defaultTriton: (options: any) => any;
export declare const defaultvLLM: (options: any) => any;
export declare const defaultTGI: (promptTemplate: string, defaultContext: string, ignoreTokens?: any) => {
    type: string;
    inputsDefaultsStream: {
        max_tokens: number;
    };
    inputsDefaults: {
        max_tokens: number;
    };
    preProcessingArgs: {
        promptTemplate: string;
        defaultContext: string;
        defaultPromptMessages: (context: string, prompt: string) => {
            role: string;
            content: string;
        }[];
    };
    postProcessingFunc: (r: any, inputs: any) => any;
    postProcessingFuncStream: (r: any, inputs: any, len: number) => any;
};
//# sourceMappingURL=defaults.d.ts.map