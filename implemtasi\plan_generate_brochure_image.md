# PLAN: Generate & Upload Brochure Images with GPT-Image-1 for Nail Art Service

## 🎯 Goal
Menghasilkan gambar digital brosur (brochure) berdasarkan prompt dari `final_openai_prompts` menggunakan GPT-Image-1, lalu mengunggah hasil gambar ke bucket Cloudflare, dan menyimpan URL hasil upload ke dalam status task.

## 📦 Module Index
1. Generate Image → Menghasilkan gambar dari setiap prompt menggunakan GPT-Image-1
2. Upload Image → Mengunggah setiap gambar ke bucket Cloudflare
3. Store Image URLs → Menyimpan URL hasil upload ke status task di Cloudflare KV

---

## 📌 Module: Generate Image

### Goal
Menghasilkan gambar digital sesuai prompt yang diberikan menggunakan GPT-Image-1.

### Input/Output

- **Input:**  
  - List prompt dari `result.brochure_prompt.final_openai_prompts`  
  - Setiap prompt memiliki field:  
    - `prompt` (string)  
    - `image_size` (string, contoh: "1024x1536")

- **Output:**  
  - List objek hasil generate:  
    - `image_binary` (buffer/binary)  
    - `prompt_index` (number, urutan prompt)
    - `prompt_text` (string, isi prompt)

### Functional Steps
1. Ambil array `final_openai_prompts` dari hasil task.
2. Untuk setiap prompt, lakukan request ke GPT-Image-1 API:
   - Kirim prompt dan image_size.
   - Terima hasil gambar dalam bentuk binary/buffer.
3. Simpan hasil generate dalam array dengan index dan prompt terkait.

### Dependencies
- API GPT-Image-1 (endpoint, key, dan format response harus sudah tersedia)
- Network access ke GPT-Image-1

---

## 📌 Module: Upload Image

### Goal
Mengunggah setiap gambar hasil generate ke bucket Cloudflare dan mendapatkan URL publiknya.

### Input/Output

- **Input:**  
  - List hasil generate image (binary/buffer)
  - Nama file unik (misal: `brochure_{task_id}_{index}.png`)
  - Bucket Cloudflare (binding/env: `BUCKET_BROCHURE` atau sesuai binding)

- **Output:**  
  - List objek hasil upload:
    - `public_url` (string, URL akses gambar)
    - `file_name` (string)
    - `prompt_index` (number)

### Functional Steps
1. Untuk setiap gambar hasil generate:
   - Buat nama file unik (gunakan task_id dan index).
   - Upload ke bucket Cloudflare.
   - Dapatkan URL publik hasil upload.
2. Simpan hasil upload (URL, nama file, index) dalam array.

### Dependencies
- Cloudflare R2 Bucket (atau storage lain yang sudah di-binding)
- Akses write ke bucket
- Konfigurasi URL publik (jika perlu signed URL, pastikan logic-nya eksplisit)

---

## 📌 Module: Store Image URLs

### Goal
Menyimpan daftar URL hasil upload ke dalam status task di Cloudflare KV.

### Input/Output

- **Input:**  
  - task_id (string)
  - List URL hasil upload (beserta index dan nama file)
  - KV binding untuk status (misal: `TASK_STATUS`)

- **Output:**  
  - Update pada KV:  
    - Key: `image_urls:{task_id}`
    - Value: JSON array URL dan metadata

### Functional Steps
1. Buat array hasil upload (index, file_name, public_url, prompt_text).
2. Simpan array ini ke KV dengan key `image_urls:{task_id}`.
3. Update status task (misal: `status:{task_id}`) jika perlu menandai proses image sudah selesai.

### Dependencies
- Cloudflare KV (TASK_STATUS)
- Akses write ke KV

---

## 🧱 Stack
- Bahasa/Framework: TypeScript (Node.js/Cloudflare Workers)
- Tool/Library wajib:
  - Cloudflare Workers API (KV, R2 Bucket)
  - HTTP client (fetch/axios) untuk GPT-Image-1

---

## 📥 Input
```json
{
  "task_id": "string",
  "result": {
    "brochure_prompt": {
      "final_openai_prompts": [
        {
          "prompt": "string",
          "image_size": "string"
        }
      ]
    }
  }
}
```

## 📤 Output
```json
{
  "status": "success" | "error",
  "image_urls": [
    {
      "index": 0,
      "file_name": "string",
      "public_url": "string",
      "prompt_text": "string"
    }
  ],
  "error": "string (jika gagal)"
}
```

---

## 🧪 Example Payload

### Example Request
```json
{
  "task_id": "a2d82320-8975-4422-90d5-912b9db343ed",
  "result": {
    "brochure_prompt": {
      "final_openai_prompts": [
        {
          "prompt": "Create a minimalist digital brochure for a nail art service...",
          "image_size": "1024x1536"
        },
        {
          "prompt": "Design a minimalist brochure for 'VE House of Beauty'...",
          "image_size": "1024x1536"
        }
      ]
    }
  }
}
```

### Example Response
```json
{
  "status": "success",
  "image_urls": [
    {
      "index": 0,
      "file_name": "brochure_a2d82320-8975-4422-90d5-912b9db343ed_0.png",
      "public_url": "https://bucket.example.com/brochure_a2d82320-8975-4422-90d5-912b9db343ed_0.png",
      "prompt_text": "Create a minimalist digital brochure for a nail art service..."
    },
    {
      "index": 1,
      "file_name": "brochure_a2d82320-8975-4422-90d5-912b9db343ed_1.png",
      "public_url": "https://bucket.example.com/brochure_a2d82320-8975-4422-90d5-912b9db343ed_1.png",
      "prompt_text": "Design a minimalist brochure for 'VE House of Beauty'..."
    }
  ]
}
```

---

## ⏳ Preconditions
- API key dan endpoint GPT-Image-1 tersedia dan valid
- Cloudflare Bucket (R2) sudah terkonfigurasi dan dapat diakses (write & public read)
- KV binding (`TASK_STATUS`) tersedia di env
- task result sudah memiliki field `brochure_prompt.final_openai_prompts` (array)
- task_id valid dan unik

---

## 📁 File & Folder Structure
- `/consumer/queues/workflow_consumer.ts` → Handler utama untuk proses queue dan trigger generate/upload image
- `/consumer/handlers/generate_brochure_image.ts` → Fungsi modular untuk generate image dari prompt
- `/consumer/handlers/upload_brochure_image.ts` → Fungsi modular untuk upload image ke bucket
- `/consumer/handlers/store_brochure_image_urls.ts` → Fungsi modular untuk simpan URL hasil upload ke KV
- Semua file harus berada di dalam satu directory utama: **/consumer/handlers/**

---

## 🧩 Functional Steps
1. Tambahkan logic pada workflow_consumer.ts untuk trigger proses generate & upload image setelah status task "completed".
2. Panggil fungsi generate_brochure_image.ts untuk setiap prompt di `final_openai_prompts`.
3. Panggil fungsi upload_brochure_image.ts untuk setiap hasil generate image.
4. Panggil fungsi store_brochure_image_urls.ts untuk menyimpan array URL hasil upload ke KV.
5. Update status task jika proses image selesai/gagal.

---

## 🔄 Logic Flow / Data Flow

```mermaid
flowchart TD
    A[Task Completed] --> B[Ambil final_openai_prompts]
    B --> C[Generate Image (GPT-Image-1)]
    C --> D[Upload ke Cloudflare Bucket]
    D --> E[Simpan URL ke KV]
    E --> F[Update status task]
```

---

## ⚠️ Edge Cases
- Jika prompt kosong atau tidak valid, skip dan log error
- Jika generate image gagal (API error), simpan error di KV dan lanjut ke prompt berikutnya
- Jika upload gagal, simpan error di KV dan lanjut ke prompt berikutnya
- Jika KV tidak bisa diakses, log error dan return status error
- Jika jumlah prompt sangat banyak (>10), batasi maksimal 10 per task (fail fast)

---

## 🔗 Dependencies & Execution Order

- Dependency: GPT-Image-1 API → digunakan oleh generate_brochure_image.ts  
  - Harus dikerjakan sebelum upload_brochure_image.ts
- Dependency: Cloudflare R2 Bucket → digunakan oleh upload_brochure_image.ts  
  - Harus dikerjakan sebelum store_brochure_image_urls.ts
- Dependency: Cloudflare KV (TASK_STATUS) → digunakan oleh store_brochure_image_urls.ts  
  - Harus dikerjakan setelah upload selesai
- Semua file harus dibuat di `/consumer/handlers/`
- Update logic di `/consumer/queues/workflow_consumer.ts` setelah semua handler tersedia

---

Apakah Anda puas dengan rencana ini? 