# PLAN: Agent Deteksi CTA Button & HTML Mapping dari Hasil generateImagesFromBrochures

## 🎯 Goal
Menerima array hasil gambar dari agent `generateImagesFromBrochures`, mendeteksi tombol CTA pada setiap gambar, lalu mengembalikan array HTML string (satu untuk setiap gambar) sesuai format di `cta_button_mapping.txt`.

## 📦 Module Index
1. Modul Input Handler → Menerima dan memvalidasi array input gambar
2. Modul CTA Detection → Mendeteksi posisi tombol CTA pada gambar
3. Modul Area Enlarger → Memperbesar area tombol CTA (2x, tetap terpusat)
4. Modul HTML Generator → Membuat HTML string mapping area sesuai spesifikasi
5. Modul Error Handling → Menangani kasus gambar corrupt, format tidak didukung, atau CTA tidak ditemukan

## 📌 Module: Input Handler

### Goal
Menerima dan memvalidasi array input gambar dari agent `generateImagesFromBrochures`.

### Input/Output
- **Input:** Array objek gambar
- **Output:** Array objek gambar yang valid

### Functional Steps
1. Terima array input
2. Validasi setiap elemen: harus memiliki `imageUrl` (string), `width` (number), `height` (number)
3. Filter atau error jika ada elemen tidak valid

### Dependencies
- Tidak ada

## 📌 Module: CTA Detection

### Goal
Mendeteksi posisi tombol CTA pada setiap gambar.

### Input/Output
- **Input:** Objek gambar (imageUrl, width, height)
- **Output:** Koordinat tombol CTA (x1, y1, x2, y2) dalam satuan pixel

### Functional Steps
1. Download gambar dari `imageUrl`
2. Jalankan deteksi tombol CTA (menggunakan model AI atau image processing)
3. Kembalikan koordinat persegi panjang tombol CTA

### Dependencies
- Library image processing/AI detection (misal: OpenCV, TensorFlow, atau library lain sesuai stack)

## 📌 Module: Area Enlarger

### Goal
Memperbesar area tombol CTA menjadi 2x lebih besar (tetap terpusat).

### Input/Output
- **Input:** Koordinat tombol CTA (x1, y1, x2, y2)
- **Output:** Koordinat area baru (x1', y1', x2', y2')

### Functional Steps
1. Hitung pusat area tombol CTA
2. Hitung ukuran baru (2x area, tetap terpusat)
3. Pastikan area baru tidak keluar dari batas gambar

### Dependencies
- Tidak ada

## 📌 Module: HTML Generator

### Goal
Membuat HTML string mapping area sesuai format di `cta_button_mapping.txt`.

### Input/Output
- **Input:** imageUrl, width, height, koordinat area (x1, y1, x2, y2)
- **Output:** HTML string

### Functional Steps
1. Masukkan nilai ke template HTML
2. Kembalikan string HTML

### Dependencies
- Tidak ada

## 📌 Module: Error Handling

### Goal
Menangani kasus gambar corrupt, format tidak didukung, atau CTA tidak ditemukan.

### Input/Output
- **Input:** Error/kasus khusus
- **Output:** String HTML kosong atau pesan error sesuai aturan

### Functional Steps
1. Jika gambar tidak bisa diakses/format tidak didukung, kembalikan string kosong
2. Jika CTA tidak ditemukan, kembalikan string kosong

### Dependencies
- Tidak ada

## 🧱 Stack
- Bahasa/Framework: (Mohon konfirmasi, misal: Node.js, Python, TypeScript)
- Tool/Library wajib: (Mohon konfirmasi, misal: OpenCV, TensorFlow, atau library lain)

## 📥 Input
```json
[
  {
    "imageUrl": "https://.../img1.png",
    "width": 1024,
    "height": 1536
  },
  {
    "imageUrl": "https://.../img2.png",
    "width": 800,
    "height": 1200
  }
]
```

## 📤 Output
```json
[
  "<img src=\"https://.../img1.png\" width=\"1024\" height=\"1536\" usemap=\"#voucher-map\" alt=\"Your Image Alt Text\"><map name=\"voucher-map\"><area shape=\"rect\" coords=\"x1,y1,x2,y2\" href=\"your-voucher-link.html\" alt=\"Redeem Voucher\"></map>",
  "<img src=\"https://.../img2.png\" width=\"800\" height=\"1200\" usemap=\"#voucher-map\" alt=\"Your Image Alt Text\"><map name=\"voucher-map\"><area shape=\"rect\" coords=\"x1,y1,x2,y2\" href=\"your-voucher-link.html\" alt=\"Redeem Voucher\"></map>"
]
```

## 🧪 Example Payload
### Example Request
POST /detect-cta
```json
[
  {
    "imageUrl": "https://.../img1.png",
    "width": 1024,
    "height": 1536
  }
]
```

### Example Response
```json
[
  "<img src=\"https://.../img1.png\" width=\"1024\" height=\"1536\" usemap=\"#voucher-map\" alt=\"Your Image Alt Text\"><map name=\"voucher-map\"><area shape=\"rect\" coords=\"x1,y1,x2,y2\" href=\"your-voucher-link.html\" alt=\"Redeem Voucher\"></map>"
]
```

## ⏳ Preconditions
- Folder target implementasi sudah ada dan dapat diakses (misal: `implemtasi/cta_agent/`)
- Library image processing/AI detection sudah terpasang
- Koneksi internet untuk mengunduh gambar dari URL

## 📁 File & Folder Structure
- `/implemtasi/cta_agent/inputHandler.js` → Modul validasi dan parsing input
- `/implemtasi/cta_agent/ctaDetector.js` → Modul deteksi tombol CTA pada gambar
- `/implemtasi/cta_agent/areaEnlarger.js` → Modul perbesaran area tombol CTA
- `/implemtasi/cta_agent/htmlGenerator.js` → Modul pembuat HTML string mapping area
- `/implemtasi/cta_agent/errorHandler.js` → Modul penanganan error dan edge case
- `/implemtasi/cta_agent/index.js` → Entry point agent, mengorkestrasi seluruh modul di atas

## 🧩 Functional Steps
1. Terima array input gambar dari user/agent lain
2. Validasi dan parsing input menggunakan `inputHandler.js`
3. Untuk setiap gambar:
    - Download gambar dari URL
    - Deteksi tombol CTA menggunakan `ctaDetector.js`
    - Jika ditemukan, perbesar area menggunakan `areaEnlarger.js`
    - Buat HTML string menggunakan `htmlGenerator.js`
    - Jika error/CTA tidak ditemukan, gunakan `errorHandler.js`
4. Kembalikan array HTML string sebagai output

## ⚠️ Edge Cases
- Jika gambar tidak bisa diakses/format tidak didukung → output string kosong
- Jika tombol CTA tidak ditemukan → output string kosong
- Jika area hasil perbesaran keluar dari batas gambar → area dipotong agar tetap dalam batas gambar

## 🔗 Dependencies & Execution Order
- Dependency: Library image processing/AI detection (misal: OpenCV, TensorFlow, atau lain sesuai stack) → digunakan oleh `ctaDetector.js`
- Harus dikerjakan sebelum: Langkah deteksi CTA
- Dependency: Folder `/implemtasi/cta_agent/` harus ada sebelum penulisan file modul
- Dependency: Koneksi internet untuk download gambar dari URL

</rewritten_file> 