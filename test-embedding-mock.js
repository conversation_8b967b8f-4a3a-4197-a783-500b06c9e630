// Mock test script for embedding functionality
// Run with: node test-embedding-mock.js

const crypto = require('crypto');

// Mock OpenAI client
class MockOpenAI {
  constructor(config) {
    this.apiKey = config.apiKey;
  }

  get embeddings() {
    return {
      create: async (params) => {
        console.log(`🔄 Mock: Generating embedding for "${params.input.substring(0, 50)}..."`);

        // Generate deterministic mock embedding for consistency
        let seed = params.input.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const random = () => {
          const x = Math.sin(seed++) * 10000;
          return (x - Math.floor(x)) * 2 - 1; // Range -1 to 1
        };

        const embedding = Array.from({ length: 1536 }, () => random());

        return {
          data: [{
            embedding: embedding,
            index: 0,
            object: 'embedding'
          }],
          model: params.model,
          object: 'list',
          usage: {
            prompt_tokens: params.input.split(' ').length,
            total_tokens: params.input.split(' ').length
          }
        };
      }
    };
  }
}

// Mock environment with all required services
const mockEnv = {
  OPENAI_API_KEY: 'mock-api-key',
  PROMPT: {
    data: new Map([
      ['prompt_audience_analysis', 'You are an AI assistant that analyzes audience data and provides insights.'],
      ['prompt-audience-profiling', 'Create detailed audience profiles based on demographic and behavioral data.'],
      ['test-prompt', 'This is a test system prompt for embedding context retrieval.']
    ]),
    async get(key) {
      return this.data.get(key) || null;
    },
    async put(key, value) {
      this.data.set(key, value);
      console.log(`✅ PROMPT KV: Stored ${key}`);
    }
  },
  VECTORIZE: {
    vectors: [],
    async upsert(vectors) {
      this.vectors.push(...vectors);
      console.log(`✅ Vectorize: Upserted ${vectors.length} vectors`);
      return { count: vectors.length };
    },
    async query(options) {
      console.log(`🔍 Vectorize Query: topK=${options.topK}, filter=${JSON.stringify(options.filter)}`);

      const matches = this.vectors
        .filter(vector => {
          if (!options.filter) return true;
          return Object.entries(options.filter).every(([key, value]) =>
            vector.metadata && vector.metadata[key] === value
          );
        })
        .map(vector => ({
          id: vector.id,
          score: Math.random() * 0.3 + 0.7, // Score between 0.7-1.0
          values: vector.values,
          metadata: vector.metadata,
          document: vector.metadata?.text || '' // Mock document content
        }));

      matches.sort((a, b) => b.score - a.score);
      return {
        matches: matches.slice(0, options.topK || 5)
      };
    }
  }
};

// Implementation of getEmbedding function
async function getEmbedding(text, env) {
  const openai = new MockOpenAI({ apiKey: env.OPENAI_API_KEY });
  const embeddingRes = await openai.embeddings.create({
    model: "text-embedding-ada-002",
    input: text
  });
  return embeddingRes.data[0].embedding;
}

// Test 1: Basic getEmbedding function
async function testGetEmbedding() {
  console.log('\n🧪 Test 1: getEmbedding Function');
  console.log('================================');

  const testCases = [
    "Machine learning is a subset of artificial intelligence",
    "Deep learning uses neural networks with multiple layers",
    "Cloudflare Workers is a serverless platform",
    "Vector databases enable similarity search"
  ];

  const embeddings = [];

  for (const text of testCases) {
    console.log(`\nTesting: "${text}"`);

    const embedding = await getEmbedding(text, mockEnv);

    // Validate embedding
    console.log(`✅ Generated embedding:`);
    console.log(`   - Type: ${typeof embedding}`);
    console.log(`   - Is Array: ${Array.isArray(embedding)}`);
    console.log(`   - Length: ${embedding.length}`);
    console.log(`   - Sample: [${embedding.slice(0, 3).map(v => v.toFixed(4)).join(', ')}...]`);
    console.log(`   - Range: ${Math.min(...embedding).toFixed(4)} to ${Math.max(...embedding).toFixed(4)}`);

    // Validation checks
    if (!Array.isArray(embedding)) {
      throw new Error('Embedding must be an array');
    }
    if (embedding.length !== 1536) {
      throw new Error(`Expected 1536 dimensions, got ${embedding.length}`);
    }
    if (embedding.some(val => typeof val !== 'number' || isNaN(val))) {
      throw new Error('All embedding values must be valid numbers');
    }

    embeddings.push({ text, embedding });
  }

  console.log(`\n✅ All ${embeddings.length} embeddings generated successfully!`);
  return embeddings;
}

// Test 2: /ingest endpoint simulation
async function testIngestEndpoint() {
  console.log('\n🧪 Test 2: /ingest Endpoint Simulation');
  console.log('======================================');

  const testDocuments = [
    {
      text: "Artificial intelligence is transforming how businesses operate and make decisions.",
      session_id: "test-session-123",
      agent: "marketing-agent"
    },
    {
      text: "Machine learning algorithms can analyze large datasets to identify patterns and trends.",
      session_id: "test-session-123",
      agent: "analytics-agent"
    },
    {
      text: "Natural language processing enables computers to understand and generate human language.",
      session_id: "test-session-456",
      agent: "nlp-agent"
    }
  ];

  const ingestResults = [];

  for (let i = 0; i < testDocuments.length; i++) {
    const doc = testDocuments[i];
    console.log(`\nIngesting document ${i + 1}/${testDocuments.length}:`);
    console.log(`Text: "${doc.text.substring(0, 60)}..."`);
    console.log(`Session: ${doc.session_id}, Agent: ${doc.agent}`);

    try {
      // Simulate /ingest endpoint logic
      const embedding = await getEmbedding(doc.text, mockEnv);
      const vectorId = crypto.randomUUID();

      await mockEnv.VECTORIZE.upsert([{
        id: vectorId,
        values: embedding,
        metadata: {
          session_id: doc.session_id,
          agent: doc.agent,
          text: doc.text // Store text for retrieval
        }
      }]);

      const result = { status: 'ok', id: vectorId };
      console.log(`✅ Ingest successful! Vector ID: ${vectorId}`);

      ingestResults.push({
        ...doc,
        vectorId,
        embedding
      });

    } catch (error) {
      console.error(`❌ Ingest failed: ${error.message}`);
      throw error;
    }
  }

  console.log(`\n📊 Ingest Summary: ${ingestResults.length}/${testDocuments.length} documents processed`);
  return ingestResults;
}

// Test 3: /context endpoint simulation
async function testContextEndpoint() {
  console.log('\n🧪 Test 3: /context Endpoint Simulation');
  console.log('=======================================');

  const testCases = [
    {
      system_prompt: 'prompt_audience_analysis',
      session_id: 'test-session-123',
      description: 'Audience analysis prompt'
    },
    {
      system_prompt: 'prompt-audience-profiling',
      session_id: 'test-session-123',
      description: 'Audience profiling prompt'
    },
    {
      system_prompt: 'test-prompt',
      session_id: 'test-session-456',
      description: 'Test prompt'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\nTesting context retrieval:`);
    console.log(`System Prompt: ${testCase.system_prompt}`);
    console.log(`Session ID: ${testCase.session_id}`);

    try {
      // Simulate /context endpoint logic
      const systemPromptKV = await mockEnv.PROMPT.get(testCase.system_prompt);

      if (!systemPromptKV) {
        console.log(`❌ System prompt not found: ${testCase.system_prompt}`);
        continue;
      }

      console.log(`✅ System prompt found: "${systemPromptKV.substring(0, 50)}..."`);

      const embedding = await getEmbedding(systemPromptKV, mockEnv);
      console.log(`✅ Generated query embedding (${embedding.length} dimensions)`);

      // Validate embedding dimensions
      if (!embedding || !Array.isArray(embedding) || embedding.length !== 1536) {
        console.log(`❌ Invalid embedding: length=${embedding?.length}`);
        continue;
      }

      const results = await mockEnv.VECTORIZE.query({
        topK: 5,
        vector: embedding,
        filter: { session_id: testCase.session_id },
        returnMetadata: true
      });

      console.log(`✅ Query successful! Found ${results.matches.length} matches`);

      const combinedContext = results.matches
        .map(match => match.document || match.metadata?.text || '')
        .join('\n\n');

      console.log(`📄 Combined context length: ${combinedContext.length} characters`);
      if (combinedContext) {
        console.log(`📄 Context preview: "${combinedContext.substring(0, 100)}..."`);
      }

      // Simulate response
      const response = { context: combinedContext };
      console.log(`✅ Context endpoint response ready`);

    } catch (error) {
      console.error(`❌ Context retrieval failed: ${error.message}`);
      throw error;
    }
  }
}

// Test 4: Error handling
async function testErrorHandling() {
  console.log('\n🧪 Test 4: Error Handling');
  console.log('=========================');

  console.log('\nTesting missing system prompt...');
  try {
    const systemPromptKV = await mockEnv.PROMPT.get('non-existent-prompt');
    if (!systemPromptKV) {
      console.log('✅ Correctly handled missing system prompt (404 expected)');
    }
  } catch (error) {
    console.error(`❌ Unexpected error: ${error.message}`);
  }

  console.log('\nTesting invalid embedding dimensions...');
  try {
    // Simulate invalid embedding
    const invalidEmbedding = [1, 2, 3]; // Wrong dimensions
    if (!invalidEmbedding || !Array.isArray(invalidEmbedding) || invalidEmbedding.length !== 1536) {
      console.log('✅ Correctly detected invalid embedding dimensions (500 expected)');
    }
  } catch (error) {
    console.error(`❌ Unexpected error: ${error.message}`);
  }

  console.log('\nTesting empty text input...');
  try {
    const embedding = await getEmbedding('', mockEnv);
    console.log(`✅ Handled empty text: ${embedding.length} dimensions`);
  } catch (error) {
    console.log(`⚠️  Empty text error: ${error.message}`);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Mock Embedding Tests');
  console.log('================================');

  try {
    const results = {};

    results.embeddings = await testGetEmbedding();
    results.ingest = await testIngestEndpoint();
    await testContextEndpoint();
    await testErrorHandling();

    console.log('\n🎉 All Mock Tests Completed Successfully!');
    console.log('========================================');
    console.log('📊 Test Summary:');
    console.log(`   ✅ Embedding generation: ${results.embeddings.length} tests`);
    console.log(`   ✅ Ingest simulation: ${results.ingest.length} documents`);
    console.log(`   ✅ Context retrieval: Multiple scenarios`);
    console.log(`   ✅ Error handling: Edge cases covered`);
    console.log(`   ✅ Total vectors in mock DB: ${mockEnv.VECTORIZE.vectors.length}`);

    return results;

  } catch (error) {
    console.error('\n💥 Mock tests failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests if executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testGetEmbedding,
  testIngestEndpoint,
  testContextEndpoint,
  testErrorHandling,
  getEmbedding,
  mockEnv
};
