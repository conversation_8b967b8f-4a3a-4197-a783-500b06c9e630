{"clientTcpRtt": 12, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 17451, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "ID", "region": "Central Java", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "cm7tk/Bb0iOmQZ7OMKfFaDD12uvaD1Vu8h3EG2vf8KM=", "tlsExportedAuthenticator": {"clientFinished": "3097aa4727b9209a5e2846b9dbef84d2118dc8f92d361e7e7cb0b2ec42a3f8d0db5a2d08b9eef899adfbca765de0d837", "clientHandshake": "3220a537edc0bc79265cb06196f564dda87e96a3e72c814f3e0eff7fff282237cd9d0274c65862143c7285516f0904d8", "serverHandshake": "68dc0faa9c586583d391337171842a068f5204ad127efc40bb086898392866fdb9b7a7c587194275fb4a12a7a5dadabf", "serverFinished": "e3c73333923eac9691c8086cae2a734e10451110e85bea0e3525464c92d8145c3c8cc96b29a7b457fabf6facc8408ef2"}, "tlsClientHelloLength": "386", "colo": "CGK", "timezone": "Asia/Jakarta", "longitude": "110.47530", "latitude": "-7.27220", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "50148", "city": "Semarang", "tlsVersion": "TLSv1.3", "regionCode": "JT", "asOrganization": "Biznet Networks", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}