// Test script untuk local development
const BASE_URL = 'http://127.0.0.1:8787';

async function makeRequest(endpoint, method = 'GET', body = null) {
  const url = `${BASE_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const responseText = await response.text();
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Response: ${JSON.stringify(responseData, null, 2)}`);
    
    return {
      status: response.status,
      data: responseData,
      ok: response.ok
    };
    
  } catch (error) {
    console.error(`❌ Request failed: ${error.message}`);
    throw error;
  }
}

async function testLocalContext() {
  console.log('🧪 Testing Local Context Endpoint');
  console.log('=================================');
  console.log('⚠️  Note: Vectorize is not supported in local dev mode');
  
  // Test 1: Check if basic endpoints work
  console.log('\n📋 Test 1: List system prompts (should work)');
  try {
    const result = await makeRequest('/list-system-prompts');
    if (result.ok) {
      console.log(`✅ System prompts working in local mode`);
      console.log(`   Found ${result.data.keys?.length || 0} prompts`);
    }
  } catch (error) {
    console.log(`❌ System prompts failed: ${error.message}`);
  }
  
  // Test 2: Test /ingest (should fail because of Vectorize)
  console.log('\n📥 Test 2: Test /ingest endpoint');
  try {
    const result = await makeRequest('/ingest', 'POST', {
      text: "Test content for local development",
      session_id: "local-test-123",
      agent: "local-agent"
    });
    
    if (result.ok) {
      console.log(`✅ Ingest working (unexpected!)`);
    } else {
      console.log(`❌ Ingest failed as expected: ${result.status}`);
      console.log(`   Reason: Vectorize not supported in local mode`);
    }
  } catch (error) {
    console.log(`❌ Ingest failed: ${error.message}`);
  }
  
  // Test 3: Test /context (should fail because of Vectorize)
  console.log('\n🔍 Test 3: Test /context endpoint');
  try {
    const result = await makeRequest('/context', 'POST', {
      system_prompt: 'test-prompt',
      session_id: 'local-test-123'
    });
    
    if (result.ok) {
      console.log(`✅ Context working (unexpected!)`);
    } else {
      console.log(`❌ Context failed as expected: ${result.status}`);
      console.log(`   Reason: Vectorize not supported in local mode`);
    }
  } catch (error) {
    console.log(`❌ Context failed: ${error.message}`);
  }
}

async function explainLocalLimitations() {
  console.log('\n💡 Local Development Limitations');
  console.log('================================');
  
  console.log('❌ TIDAK BISA DI TEST LOCAL:');
  console.log('   - /ingest endpoint (butuh Vectorize)');
  console.log('   - /context endpoint (butuh Vectorize)');
  console.log('   - Semua fitur embedding/vector search');
  
  console.log('\n✅ BISA DI TEST LOCAL:');
  console.log('   - System prompt management (/add-system-prompt, /get-system-prompt, /list-system-prompts)');
  console.log('   - Agent endpoints (/audience-analysis, /ads-copy-generation, dll)');
  console.log('   - Image generation endpoints');
  console.log('   - Workflow endpoints');
  
  console.log('\n🔧 SOLUSI UNTUK TEST EMBEDDING:');
  console.log('   1. Deploy ke production: wrangler deploy');
  console.log('   2. Test di production URL: https://ai.klikbuy.co.id');
  console.log('   3. Atau gunakan flag: --experimental-vectorize-bind-to-prod');
  
  console.log('\n📋 KENAPA ERROR 500 DI PRODUCTION:');
  console.log('   - System prompt terlalu panjang (>2000 chars)');
  console.log('   - OpenAI API timeout');
  console.log('   - Tidak ada error handling yang proper');
  console.log('   - Fix sudah dibuat, perlu di-deploy');
}

async function testWithProdVectorize() {
  console.log('\n🧪 Testing with Production Vectorize');
  console.log('====================================');
  console.log('⚠️  This requires restarting wrangler with --experimental-vectorize-bind-to-prod');
  console.log('');
  console.log('Commands to run:');
  console.log('1. Stop current wrangler (Ctrl+C)');
  console.log('2. Run: npx wrangler dev --experimental-vectorize-bind-to-prod');
  console.log('3. Test again with this script');
  console.log('');
  console.log('⚠️  WARNING: This will use production Vectorize database!');
}

async function runTest() {
  console.log('🔧 Local Development Context Test');
  console.log('=================================');
  
  await testLocalContext();
  await explainLocalLimitations();
  await testWithProdVectorize();
  
  console.log('\n🎯 Summary');
  console.log('==========');
  console.log('❌ /context endpoint tidak bisa di test di local');
  console.log('❌ Vectorize tidak support local development');
  console.log('✅ Fix sudah dibuat di code');
  console.log('✅ Perlu deploy ke production untuk test');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy fix: cd producer && wrangler deploy');
  console.log('2. Test di production: node diagnose-context-error.js');
  console.log('3. Atau gunakan --experimental-vectorize-bind-to-prod untuk local test');
}

if (require.main === module) {
  runTest();
}

module.exports = { runTest, testLocalContext, explainLocalLimitations };
