"use strict";require("base64-js");var e=require("mustache");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(s){if("default"!==s){var n=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,n.get?n:{enumerable:!0,get:function(){return e[s]}})}})),t.default=e,Object.freeze(t)}var s,n=t(e);!function(e){e.String="str",e.Bool="bool",e.Float16="float16",e.Float32="float32",e.Int16="int16",e.Int32="int32",e.Int64="int64",e.Int8="int8",e.Uint16="uint16",e.Uint32="uint32",e.Uint64="uint64",e.Uint8="uint8"}(s||(s={}));const r=Object.getPrototypeOf(Uint8Array);function a(e){return Array.isArray(e)||e instanceof r}function o(e){return e instanceof r?e.length:e.flat(1/0).reduce(((e,t)=>e+(t instanceof r?t.length:1)),0)}function i(e,t){if(!a(t)){switch(e){case s.Bool:if("boolean"==typeof t)return;break;case s.Float16:case s.Float32:if("number"==typeof t)return;break;case s.Int8:case s.Uint8:case s.Int16:case s.Uint16:case s.Int32:case s.Uint32:if(Number.isInteger(t))return;break;case s.Int64:case s.Uint64:if("bigint"==typeof t)return;break;case s.String:if("string"==typeof t)return}throw new Error(`unexpected type "${e}" with value "${t}".`)}t.forEach((t=>i(e,t)))}function E(e,t){if(a(t))return[...t].map((t=>E(e,t)));switch(e){case s.String:case s.Bool:case s.Float16:case s.Float32:case s.Int8:case s.Uint8:case s.Int16:case s.Uint16:case s.Uint32:case s.Int32:return t;case s.Int64:case s.Uint64:return t.toString()}throw new Error(`unexpected type "${e}" with value "${t}".`)}function p(e,t){if(a(t))return t.map((t=>p(e,t)));switch(e){case s.String:case s.Bool:case s.Float16:case s.Float32:case s.Int8:case s.Uint8:case s.Int16:case s.Uint16:case s.Uint32:case s.Int32:return t;case s.Int64:case s.Uint64:return BigInt(t)}throw new Error(`unexpected type "${e}" with value "${t}".`)}class A{type;value;name;shape;constructor(e,t,s={}){this.type=e,this.value=t,s.validate&&i(e,this.value),void 0===s.shape?a(this.value)?this.shape=[o(t)]:this.shape=[]:this.shape=s.shape,s.validate&&function(e,t){if(0===e.length&&!a(t))return;const s=e.reduce(((e,t)=>{if(!Number.isInteger(t))throw new Error(`expected shape to be array-like of integers but found non-integer element "${t}"`);return e*t}),1);if(s!=o(t))throw new Error(`invalid shape: expected ${s} elements for shape ${e} but value array has length ${t.length}`)}(this.shape,this.value),this.name=s.name||null}static fromJSON(e){const{type:t,shape:s,value:n,b64Value:r,name:a}=e,o={shape:s,name:a};if(void 0!==r){const e=function(e,t){const s=atob(e),n=new Uint8Array(s.length);for(let e=0;e<s.length;e++)n[e]=s.charCodeAt(e);const r=new DataView(n.buffer).buffer;switch(t){case"float32":return new Float32Array(r);case"float64":return new Float64Array(r);case"int32":return new Int32Array(r);case"int64":return new BigInt64Array(r);default:throw Error(`invalid data type for base64 input: ${t}`)}}(r,t)[0];return new A(t,e,o)}return new A(t,p(t,n),o)}toJSON(){return{type:this.type,shape:this.shape,name:this.name,value:E(this.type,this.value)}}}const R="A chat between a curious human and an artificial intelligence assistant. The assistant gives helpful, detailed, and polite answers to the human's questions.",c="Write code to solve the following coding problem that obeys the constraints and passes the example test cases. Please wrap your code answer using   ```:",m=(e,t)=>[{role:"system",content:e},{role:"user",content:t}],u=e=>{const t={};e.temperature&&(t.temperature=e.temperature),e.max_tokens&&(t.max_tokens=e.max_tokens);const n=[new A(s.String,[e.prompt],{shape:[1],name:"text_input"}),new A(s.String,[JSON.stringify(t)],{shape:[1],name:"sampling_parameters"})];return e.stream&&n.push(new A(s.Bool,!0,{name:"stream"})),e.image&&(n.push(new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"image"})),n.push(new A(s.Bool,!0,{name:"exclude_input_in_output"}))),n},O=(e,t)=>{let s=e.generated_text.value[0];if(t)for(const e in t)s=s.replace(t[e],"");return s},l=e=>(e.inputsDefaultsStream={max_tokens:1800,...e.inputsDefaultsStream||{}},e.inputsDefaults={max_tokens:256,...e.inputsDefaults||{}},e.preProcessingArgs={promptTemplate:"bare",defaultContext:R,defaultPromptMessages:m,...e.preProcessingArgs||{}},e={type:"triton",...e}),I=e=>(e.inputsDefaultsStream={max_tokens:512,...e.inputsDefaultsStream||{}},e.inputsDefaults={max_tokens:512,...e.inputsDefaults||{}},e.preProcessingArgs={promptTemplate:"bare",defaultContext:R,defaultPromptMessages:m,...e.preProcessingArgs||{}},e={type:"vllm",generateTensorsFunc:e=>u(e),postProcessingFunc:(e,t)=>e.name.value[0].slice(t.prompt.length),postProcessingFuncStream:(e,t,s)=>e.name.value[0],...e}),T=(e,t,s)=>({type:"tgi",inputsDefaultsStream:{max_tokens:512},inputsDefaults:{max_tokens:256},preProcessingArgs:{promptTemplate:e,defaultContext:t,defaultPromptMessages:m},postProcessingFunc:(e,t)=>O(e,s),postProcessingFuncStream:(e,t,n)=>O(e,s)}),S=["TENCH","GOLDFISH","WHITE SHARK","TIGER SHARK","HAMMERHEAD SHARK","ELECTRIC RAY","STINGRAY","ROOSTER","HEN","OSTRICH","BRAMBLING","GOLDFINCH","HOUSE FINCH","SNOWBIRD","INDIGO FINCH","ROBIN","BULBUL","JAY","MAGPIE","CHICKADEE","WATER OUZEL","KITE","BALD EAGLE","VULTURE","GREAT GREY OWL","FIRE SALAMANDER","NEWT","EFT","SPOTTED SALAMANDER","AXOLOTL","BULL FROG","TREE FROG","TAILED FROG","LOGGERHEAD","LEATHERBACK TURTLE","MUD TURTLE","TERRAPIN","BOX TURTLE","BANDED GECKO","COMMON IGUANA","AMERICAN CHAMELEON","WHIPTAIL","AGAMA","FRILLED LIZARD","ALLIGATOR LIZARD","GILA MONSTER","GREEN LIZARD","AFRICAN CHAMELEON","KOMODO DRAGON","AFRICAN CROCODILE","AMERICAN ALLIGATOR","TRICERATOPS","THUNDER SNAKE","RINGNECK SNAKE","HOGNOSE SNAKE","GREEN SNAKE","KING SNAKE","GARTER SNAKE","WATER SNAKE","VINE SNAKE","NIGHT SNAKE","BOA","ROCK PYTHON","COBRA","GREEN MAMBA","SEA SNAKE","HORNED VIPER","DIAMONDBACK","SIDEWINDER","TRILOBITE","HARVESTMAN","SCORPION","GARDEN SPIDER","BARN SPIDER","GARDEN SPIDER","BLACK WIDOW","TARANTULA","WOLF SPIDER","TICK","CENTIPEDE","GROUSE","PTARMIGAN","RUFFED GROUSE","PRAIRIE CHICKEN","PEACOCK","QUAIL","PARTRIDGE","AFRICAN GREY","MACAW","COCKATOO","LORIKEET","COUCAL","BEE EATER","HORNBILL","HUMMINGBIRD","JACAMAR","TOUCAN","DRAKE","MERGANSER","GOOSE","BLACK SWAN","TUSKER","ECHIDNA","PLATYPUS","WALLABY","KOALA","WOMBAT","JELLYFISH","SEA ANEMONE","BRAIN CORAL","FLATWORM","NEMATODE","CONCH","SNAIL","SLUG","SEA SLUG","CHITON","CHAMBERED NAUTILUS","DUNGENESS CRAB","ROCK CRAB","FIDDLER CRAB","KING CRAB","AMERICAN LOBSTER","SPINY LOBSTER","CRAYFISH","HERMIT CRAB","ISOPOD","WHITE STORK","BLACK STORK","SPOONBILL","FLAMINGO","LITTLE BLUE HERON","AMERICAN EGRET","BITTERN","CRANE","LIMPKIN","EUROPEAN GALLINULE","AMERICAN COOT","BUSTARD","RUDDY TURNSTONE","RED-BACKED SANDPIPER","REDSHANK","DOWITCHER","OYSTERCATCHER","PELICAN","KING PENGUIN","ALBATROSS","GREY WHALE","KILLER WHALE","DUGONG","SEA LION","CHIHUAHUA","JAPANESE SPANIEL","MALTESE DOG","PEKINESE","SHIH-TZU","BLENHEIM SPANIEL","PAPILLON","TOY TERRIER","RHODESIAN RIDGEBACK","AFGHAN HOUND","BASSET","BEAGLE","BLOODHOUND","BLUETICK","COONHOUND","WALKER HOUND","ENGLISH FOXHOUND","REDBONE","BORZOI","IRISH WOLFHOUND","ITALIAN GREYHOUND","WHIPPET","IBIZAN HOUND","NORWEGIAN ELKHOUND","OTTERHOUND","SALUKI","SCOTTISH DEERHOUND","WEIMARANER","STAFFORDSHIRE BULLTERRIER","STAFFORDSHIRE TERRIER","BEDLINGTON TERRIER","BORDER TERRIER","KERRY BLUE TERRIER","IRISH TERRIER","NORFOLK TERRIER","NORWICH TERRIER","YORKSHIRE TERRIER","WIRE-HAIRED FOX TERRIER","LAKELAND TERRIER","SEALYHAM TERRIER","AIREDALE","CAIRN","AUSTRALIAN TERRIER","DANDIE DINMONT","BOSTON BULL","MINIATURE SCHNAUZER","GIANT SCHNAUZER","STANDARD SCHNAUZER","SCOTCH TERRIER","TIBETAN TERRIER","SILKY TERRIER","WHEATEN TERRIER","WHITE TERRIER","LHASA","RETRIEVER","CURLY-COATED RETRIEVER","GOLDEN RETRIEVER","LABRADOR RETRIEVER","CHESAPEAKE BAY RETRIEVER","SHORT-HAIRED POINTER","VISLA","ENGLISH SETTER","IRISH SETTER","GORDON SETTER","BRITTANY SPANIEL","CLUMBER","ENGLISH SPRINGER","WELSH SPRINGER SPANIEL","COCKER SPANIEL","SUSSEX SPANIEL","IRISH WATERSPANIEL","KUVASZ","SCHIPPERKE","GROENENDAEL","MALINOIS","BRIARD","KELPIE","KOMONDOR","OLD ENGLISH SHEEPDOG","SHETLAND SHEEPDOG","COLLIE","BORDER COLLIE","BOUVIER DES FLANDRES","ROTTWEILER","GERMAN SHEPHERD","DOBERMAN","MINIATURE PINSCHER","GREATER SWISS MOUNTAIN DOG","BERNESE MOUNTAIN DOG","APPENZELLER","ENTLEBUCHER","BOXER","BULL MASTIFF","TIBETAN MASTIFF","FRENCH BULLDOG","GREAT DANE","SAINT BERNARD","ESKIMO DOG","MALAMUTE","SIBERIAN HUSKY","DALMATIAN","AFFENPINSCHER","BASENJI","PUG","LEONBERG","NEWFOUNDLAND","GREAT PYRENEES","SAMOYED","POMERANIAN","CHOW","KEESHOND","BRABANCON GRIFFON","PEMBROKE","CARDIGAN","TOY POODLE","MINIATURE POODLE","STANDARD POODLE","MEXICAN HAIRLESS","TIMBER WOLF","WHITE WOLF","RED WOLF","COYOTE","DINGO","DHOLE","AFRICAN HUNTING DOG","HYENA","RED FOX","KIT FOX","ARCTIC FOX","GREY FOX","TABBY","TIGER CAT","PERSIAN CAT","SIAMESE CAT","EGYPTIAN CAT","COUGAR","LYNX","LEOPARD","SNOW LEOPARD","JAGUAR","LION","TIGER","CHEETAH","BROWN BEAR","AMERICAN BLACK BEAR","ICE BEAR","SLOTH BEAR","MONGOOSE","MEERKAT","TIGER BEETLE","LADYBUG","GROUND BEETLE","LONG-HORNED BEETLE","LEAF BEETLE","DUNG BEETLE","RHINOCEROS BEETLE","WEEVIL","FLY","BEE","ANT","GRASSHOPPER","CRICKET","WALKING STICK","COCKROACH","MANTIS","CICADA","LEAFHOPPER","LACEWING","DRAGONFLY","DAMSELFLY","ADMIRAL","RINGLET","MONARCH","CABBAGE BUTTERFLY","SULPHUR BUTTERFLY","LYCAENID","STARFISH","SEA URCHIN","SEA CUCUMBER","WOOD RABBIT","HARE","ANGORA","HAMSTER","PORCUPINE","FOX SQUIRREL","MARMOT","BEAVER","GUINEA PIG","SORREL","ZEBRA","HOG","WILD BOAR","WARTHOG","HIPPOPOTAMUS","OX","WATER BUFFALO","BISON","RAM","BIGHORN","IBEX","HARTEBEEST","IMPALA","GAZELLE","ARABIAN CAMEL","LLAMA","WEASEL","MINK","POLECAT","BLACK-FOOTED FERRET","OTTER","SKUNK","BADGER","ARMADILLO","THREE-TOED SLOTH","ORANGUTAN","GORILLA","CHIMPANZEE","GIBBON","SIAMANG","GUENON","PATAS","BABOON","MACAQUE","LANGUR","COLOBUS","PROBOSCIS MONKEY","MARMOSET","CAPUCHIN","HOWLER MONKEY","TITI","SPIDER MONKEY","SQUIRREL MONKEY","MADAGASCAR CAT","INDRI","INDIAN ELEPHANT","AFRICAN ELEPHANT","LESSER PANDA","GIANT PANDA","BARRACOUTA","EEL","COHO","ROCK BEAUTY","ANEMONE FISH","STURGEON","GAR","LIONFISH","PUFFER","ABACUS","ABAYA","ACADEMIC GOWN","ACCORDION","ACOUSTIC GUITAR","AIRCRAFT CARRIER","AIRLINER","AIRSHIP","ALTAR","AMBULANCE","AMPHIBIAN","ANALOG CLOCK","APIARY","APRON","ASHCAN","ASSAULT RIFLE","BACKPACK","BAKERY","BALANCE BEAM","BALLOON","BALLPOINT","BAND AID","BANJO","BANNISTER","BARBELL","BARBER CHAIR","BARBERSHOP","BARN","BAROMETER","BARREL","BARROW","BASEBALL","BASKETBALL","BASSINET","BASSOON","BATHING CAP","BATH TOWEL","BATHTUB","BEACH WAGON","BEACON","BEAKER","BEARSKIN","BEER BOTTLE","BEER GLASS","BELL COTE","BIB","BICYCLE-BUILT-FOR-TWO","BIKINI","BINDER","BINOCULARS","BIRDHOUSE","BOATHOUSE","BOBSLED","BOLO TIE","BONNET","BOOKCASE","BOOKSHOP","BOTTLECAP","BOW","BOW TIE","BRASS","BRASSIERE","BREAKWATER","BREASTPLATE","BROOM","BUCKET","BUCKLE","BULLETPROOF VEST","BULLET TRAIN","BUTCHER SHOP","CAB","CALDRON","CANDLE","CANNON","CANOE","CAN OPENER","CARDIGAN","CAR MIRROR","CAROUSEL","CARPENTERS KIT","CARTON","CAR WHEEL","CASH MACHINE","CASSETTE","CASSETTE PLAYER","CASTLE","CATAMARAN","CD PLAYER","CELLO","CELLULAR TELEPHONE","CHAIN","CHAINLINK FENCE","CHAIN MAIL","CHAIN SAW","CHEST","CHIFFONIER","CHIME","CHINA CABINET","CHRISTMAS STOCKING","CHURCH","CINEMA","CLEAVER","CLIFF DWELLING","CLOAK","CLOG","COCKTAIL SHAKER","COFFEE MUG","COFFEEPOT","COIL","COMBINATION LOCK","COMPUTER KEYBOARD","CONFECTIONERY","CONTAINER SHIP","CONVERTIBLE","CORKSCREW","CORNET","COWBOY BOOT","COWBOY HAT","CRADLE","CRANE","CRASH HELMET","CRATE","CRIB","CROCK POT","CROQUET BALL","CRUTCH","CUIRASS","DAM","DESK","DESKTOP COMPUTER","DIAL TELEPHONE","DIAPER","DIGITAL CLOCK","DIGITAL WATCH","DINING TABLE","DISHRAG","DISHWASHER","DISK BRAKE","DOCK","DOGSLED","DOME","DOORMAT","DRILLING PLATFORM","DRUM","DRUMSTICK","DUMBBELL","DUTCH OVEN","ELECTRIC FAN","ELECTRIC GUITAR","ELECTRIC LOCOMOTIVE","ENTERTAINMENT CENTER","ENVELOPE","ESPRESSO MAKER","FACE POWDER","FEATHER BOA","FILE","FIREBOAT","FIRE ENGINE","FIRE SCREEN","FLAGPOLE","FLUTE","FOLDING CHAIR","FOOTBALL HELMET","FORKLIFT","FOUNTAIN","FOUNTAIN PEN","FOUR-POSTER","FREIGHT CAR","FRENCH HORN","FRYING PAN","FUR COAT","GARBAGE TRUCK","GASMASK","GAS PUMP","GOBLET","GO-KART","GOLF BALL","GOLFCART","GONDOLA","GONG","GOWN","GRAND PIANO","GREENHOUSE","GRILLE","GROCERY STORE","GUILLOTINE","HAIR SLIDE","HAIR SPRAY","HALF TRACK","HAMMER","HAMPER","HAND BLOWER","HAND-HELD COMPUTER","HANDKERCHIEF","HARD DISC","HARMONICA","HARP","HARVESTER","HATCHET","HOLSTER","HOME THEATER","HONEYCOMB","HOOK","HOOPSKIRT","HORIZONTAL BAR","HORSE CART","HOURGLASS","IPOD","IRON","JACK-O-LANTERN","JEAN","JEEP","JERSEY","JIGSAW PUZZLE","JINRIKISHA","JOYSTICK","KIMONO","KNEE PAD","KNOT","LAB COAT","LADLE","LAMPSHADE","LAPTOP","LAWN MOWER","LENS CAP","LETTER OPENER","LIBRARY","LIFEBOAT","LIGHTER","LIMOUSINE","LINER","LIPSTICK","LOAFER","LOTION","LOUDSPEAKER","LOUPE","LUMBERMILL","MAGNETIC COMPASS","MAILBAG","MAILBOX","MAILLOT","MAILLOT","MANHOLE COVER","MARACA","MARIMBA","MASK","MATCHSTICK","MAYPOLE","MAZE","MEASURING CUP","MEDICINE CHEST","MEGALITH","MICROPHONE","MICROWAVE","MILITARY UNIFORM","MILK CAN","MINIBUS","MINISKIRT","MINIVAN","MISSILE","MITTEN","MIXING BOWL","MOBILE HOME","MODEL T","MODEM","MONASTERY","MONITOR","MOPED","MORTAR","MORTARBOARD","MOSQUE","MOSQUITO NET","MOTOR SCOOTER","MOUNTAIN BIKE","MOUNTAIN TENT","MOUSE","MOUSETRAP","MOVING VAN","MUZZLE","NAIL","NECK BRACE","NECKLACE","NIPPLE","NOTEBOOK","OBELISK","OBOE","OCARINA","ODOMETER","OIL FILTER","ORGAN","OSCILLOSCOPE","OVERSKIRT","OXCART","OXYGEN MASK","PACKET","PADDLE","PADDLEWHEEL","PADLOCK","PAINTBRUSH","PAJAMA","PALACE","PANPIPE","PAPER TOWEL","PARACHUTE","PARALLEL BARS","PARK BENCH","PARKING METER","PASSENGER CAR","PATIO","PAY-PHONE","PEDESTAL","PENCIL BOX","PENCIL SHARPENER","PERFUME","PETRI DISH","PHOTOCOPIER","PICK","PICKELHAUBE","PICKET FENCE","PICKUP","PIER","PIGGY BANK","PILL BOTTLE","PILLOW","PING-PONG BALL","PINWHEEL","PIRATE","PITCHER","PLANE","PLANETARIUM","PLASTIC BAG","PLATE RACK","PLOW","PLUNGER","POLAROID CAMERA","POLE","POLICE VAN","PONCHO","POOL TABLE","POP BOTTLE","POT","POTTERS WHEEL","POWER DRILL","PRAYER RUG","PRINTER","PRISON","PROJECTILE","PROJECTOR","PUCK","PUNCHING BAG","PURSE","QUILL","QUILT","RACER","RACKET","RADIATOR","RADIO","RADIO TELESCOPE","RAIN BARREL","RECREATIONAL VEHICLE","REEL","REFLEX CAMERA","REFRIGERATOR","REMOTE CONTROL","RESTAURANT","REVOLVER","RIFLE","ROCKING CHAIR","ROTISSERIE","RUBBER ERASER","RUGBY BALL","RULE","RUNNING SHOE","SAFE","SAFETY PIN","SALTSHAKER","SANDAL","SARONG","SAX","SCABBARD","SCALE","SCHOOL BUS","SCHOONER","SCOREBOARD","SCREEN","SCREW","SCREWDRIVER","SEAT BELT","SEWING MACHINE","SHIELD","SHOE SHOP","SHOJI","SHOPPING BASKET","SHOPPING CART","SHOVEL","SHOWER CAP","SHOWER CURTAIN","SKI","SKI MASK","SLEEPING BAG","SLIDE RULE","SLIDING DOOR","SLOT","SNORKEL","SNOWMOBILE","SNOWPLOW","SOAP DISPENSER","SOCCER BALL","SOCK","SOLAR DISH","SOMBRERO","SOUP BOWL","SPACE BAR","SPACE HEATER","SPACE SHUTTLE","SPATULA","SPEEDBOAT","SPIDER WEB","SPINDLE","SPORTS CAR","SPOTLIGHT","STAGE","STEAM LOCOMOTIVE","STEEL ARCH BRIDGE","STEEL DRUM","STETHOSCOPE","STOLE","STONE WALL","STOPWATCH","STOVE","STRAINER","STREETCAR","STRETCHER","STUDIO COUCH","STUPA","SUBMARINE","SUIT","SUNDIAL","SUNGLASS","SUNGLASSES","SUNSCREEN","SUSPENSION BRIDGE","SWAB","SWEATSHIRT","SWIMMING TRUNKS","SWING","SWITCH","SYRINGE","TABLE LAMP","TANK","TAPE PLAYER","TEAPOT","TEDDY","TELEVISION","TENNIS BALL","THATCH","THEATER CURTAIN","THIMBLE","THRESHER","THRONE","TILE ROOF","TOASTER","TOBACCO SHOP","TOILET SEAT","TORCH","TOTEM POLE","TOW TRUCK","TOYSHOP","TRACTOR","TRAILER TRUCK","TRAY","TRENCH COAT","TRICYCLE","TRIMARAN","TRIPOD","TRIUMPHAL ARCH","TROLLEYBUS","TROMBONE","TUB","TURNSTILE","TYPEWRITER KEYBOARD","UMBRELLA","UNICYCLE","UPRIGHT","VACUUM","VASE","VAULT","VELVET","VENDING MACHINE","VESTMENT","VIADUCT","VIOLIN","VOLLEYBALL","WAFFLE IRON","WALL CLOCK","WALLET","WARDROBE","WARPLANE","WASHBASIN","WASHER","WATER BOTTLE","WATER JUG","WATER TOWER","WHISKEY JUG","WHISTLE","WIG","WINDOW SCREEN","WINDOW SHADE","WINDSOR TIE","WINE BOTTLE","WING","WOK","WOODEN SPOON","WOOL","WORM FENCE","WRECK","YAWL","YURT","WEB SITE","COMIC BOOK","CROSSWORD PUZZLE","STREET SIGN","TRAFFIC LIGHT","BOOK JACKET","MENU","PLATE","GUACAMOLE","CONSOMME","HOT POT","TRIFLE","ICE CREAM","ICE LOLLY","FRENCH LOAF","BAGEL","PRETZEL","CHEESEBURGER","HOTDOG","MASHED POTATO","HEAD CABBAGE","BROCCOLI","CAULIFLOWER","ZUCCHINI","SPAGHETTI SQUASH","ACORN SQUASH","BUTTERNUT SQUASH","CUCUMBER","ARTICHOKE","BELL PEPPER","CARDOON","MUSHROOM","GRANNY SMITH","STRAWBERRY","ORANGE","LEMON","FIG","PINEAPPLE","BANANA","JACKFRUIT","CUSTARD APPLE","POMEGRANATE","HAY","CARBONARA","CHOCOLATE SAUCE","DOUGH","MEAT LOAF","PIZZA","POTPIE","BURRITO","RED WINE","ESPRESSO","CUP","EGGNOG","ALP","BUBBLE","CLIFF","CORAL REEF","GEYSER","LAKESIDE","PROMONTORY","SANDBAR","SEASHORE","VALLEY","VOLCANO","BALLPLAYER","GROOM","SCUBA DIVER","RAPESEED","DAISY","LADY SLIPPER","CORN","ACORN","HIP","BUCKEYE","CORAL FUNGUS","AGARIC","GYROMITRA","STINKHORN","EARTHSTAR","HEN-OF-THE-WOODS","BOLETE","EAR","TOILET TISSUE"];class g{inputs;postProcessedOutputs}class N{inputs;postProcessedOutputs}class L{inputs;postProcessedOutputs}class h{inputs;postProcessedOutputs}class P{inputs;postProcessedOutputs}class C{inputs;postProcessedOutputs}class d{inputs;postProcessedOutputs}class B{inputs;postProcessedOutputs}class H{inputs;postProcessedOutputs}class U{inputs;postProcessedOutputs}class f{inputs;postProcessedOutputs}const D={bare:"{{! https://huggingface.co/TheBloke/deepseek-coder-6.7B-base-AWQ }}{{#messages}}{{#user}}{{#countUser1}}{{#lastSystem}}{{{lastSystem}}} {{/lastSystem}}{{/countUser1}}{{{content}}}{{/user}}{{#assistant}} {{{content}}} {{/assistant}}{{/messages}}",chatml:"{{! https://huggingface.co/TheBloke/OpenHermes-2.5-Mistral-7B-AWQ#prompt-template-chatml }}{{#messages}}{{#system}}<|im_start|>system\n{{{content}}}<|im_end|>\n{{/system}}{{#user}}<|im_start|>user\n{{{content}}}<|im_end|>\n{{/user}}{{#assistant}}<|im_start|>assistant\n{{{content}}}<|im_end|>\n{{/assistant}}{{/messages}}<|im_start|>assistant\n",deepseek:"{{! https://huggingface.co/TheBloke/deepseek-coder-6.7B-instruct-AWQ }}{{#messages}}{{#system}}{{{content}}}\n{{/system}}{{#user}}### Instruction:\n{{{content}}}\n{{/user}}{{#assistant}}### Response:\n{{{content}}}\n{{/assistant}}\n{{/messages}}\n### Response:\n",falcon:"{{! https://huggingface.co/TheBloke/Falcon-7B-Instruct-GPTQ }}{{#messages}}{{#system}}{{{content}}}\n{{/system}}{{#user}}User: {{{content}}}\n{{/user}}{{#assistant}}Assistant: {{{content}}}\n{{/assistant}}{{/messages}}\nAssistant: \n{{}}",gemma:"{{! https://ai.google.dev/gemma/docs/formatting https://huggingface.co/google/gemma-7b-it }}{{#messages}}{{#user}}<start_of_turn>user\n{{{content}}}<end_of_turn>\n{{/user}}{{#assistant}}<start_of_turn>model\n{{{content}}}<end_of_turn>\n{{/assistant}}{{/messages}}<start_of_turn>model\n","hermes2-pro":"{{! https://huggingface.co/NousResearch/Hermes-2-Pro-Mistral-7B#prompt-format-for-function-calling }}{{#messages}}{{#system}}<|im_start|>system\n{{{content}}}<|im_end|>\n{{/system}}{{#user}}<|im_start|>user\n{{{content}}}<|im_end|>\n{{/user}}{{#assistant}}<|im_start|>assistant\n{{{content}}}<|im_end|>\n{{/assistant}}{{#tool_query}}<|im_start|>assistant\n<tool_call>\n{{{content}}}</tool_call><|im_end|>\n{{/tool_query}}{{#tool_response}}<|im_start|>tool\n<tool_response>\n{{{content}}}</tool_response>\n<|im_end|>\n{{/tool_response}}{{/messages}}<|im_start|>assistant\n",inst:"{{! https://huggingface.co/TheBloke/LlamaGuard-7B-AWQ }}{{#messages}}{{#user}}[INST] {{#countUser1}}{{#lastSystem}}{{{lastSystem}}} {{/lastSystem}}{{/countUser1}}{{{content}}} [/INST]{{/user}}{{#assistant}} {{{content}}} {{/assistant}}{{/messages}}",llama2:"{{! https://huggingface.co/TheBloke/Llama-2-13B-chat-AWQ#prompt-template-llama-2-chat }}{{#messages}}{{#system}}[INST] <<SYS>>\n{{{content}}}\n<</SYS>>\n\n{{/system}}{{#user}}{{^beforewasSystem}}<s>[INST] {{/beforewasSystem}}{{{content}}} [/INST]{{/user}}{{#assistant}} {{{content}}}</s>{{/assistant}}{{/messages}}",llama3:"{{! https://llama.meta.com/docs/model-cards-and-prompt-formats/meta-llama-3/ }}<|begin_of_text|>{{#messages}}{{#system}}{{#countSystem1}}<|start_header_id|>system<|end_header_id|>\n\n{{{content}}}<|eot_id|>{{/countSystem1}}{{/system}}{{#user}}<|start_header_id|>user<|end_header_id|>\n\n{{{content}}}<|eot_id|>{{/user}}{{#assistant}}<|start_header_id|>assistant<|end_header_id|>\n\n{{{content}}}<|eot_id|>{{/assistant}}{{/messages}}<|start_header_id|>assistant<|end_header_id|>\n\n",llava:"{{! https://huggingface.co/llava-hf/llava-1.5-7b-hf }}{{#messages}}{{#user}}{{#countUser1}}USER: {{{content}}}\n{{/countUser1}}{{/user}}{{/messages}}ASSISTANT:","mistral-instruct":"{{! https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-AWQ#prompt-template-mistral }}{{#messages}}{{#system}}<s>[INST] {{{content}}} {{/system}}{{#user}}{{^beforewasSystem}}[INST] {{/beforewasSystem}}{{{content}}} [/INST]{{/user}}{{#assistant}} {{{content}}}</s>{{/assistant}}{{/messages}}","openchat-alt":"{{#messages}}{{#user}}<s>Human: {{#countUser1}}{{#lastSystem}}{{{lastSystem}}} {{/lastSystem}}{{/countUser1}}{{{content}}}<|end_of_turn|>{{/user}}{{#assistant}}Assistant: {{{content}}}<|end_of_turn|>{{/assistant}}{{/messages}}Assistant: {{}}",openchat:"{{! https://huggingface.co/TheBloke/openchat_3.5-AWQ#prompt-template-openchat }}{{#messages}}{{#user}}GPT4 User: {{#countUser1}}{{#lastSystem}}{{{lastSystem}}} {{/lastSystem}}{{/countUser1}}{{{content}}}<|end_of_turn|>{{/user}}{{#assistant}}GPT4 Assistant: {{{content}}}<|end_of_turn|>{{/assistant}}{{/messages}}GPT4 Assistant:","orca-hashes":"{{! https://huggingface.co/TheBloke/neural-chat-7B-v3-1-AWQ#prompt-template-orca-hashes }}{{#messages}}{{#system}}### System:\n{{{content}}}\n\n{{/system}}{{#user}}### User:\n{{{content}}}\n\n{{/user}}{{#assistant}}### Assistant:\n{{{content}}}\n\n{{/assistant}}{{/messages}}### Assistant:\n\n","phi-2":"{{! https://www.promptingguide.ai/models/phi-2 }}{{#messages}}{{#user}}User: {{{content}}}\n{{/user}}{{#assistant}}Assistant:{{{content}}}\n{{/assistant}}{{#question}}Instruct: {{{ content }}}\nOutput: {{/question}}{{/messages}}",sqlcoder:"### Task\nGenerate a SQL query to answer [QUESTION]{{{lastUser}}}[/QUESTION]\n\n### Database Schema\nThe query will run on a database with the following schema:\n{{{lastSystem}}}\n\n### Answer\nGiven the database schema, here is the SQL query that [QUESTION]{{{lastUser}}}[/QUESTION]\n[SQL]",starling:"{{#messages}}{{#user}}GPT4 Correct User: {{#countUser1}}{{#lastSystem}}{{{lastSystem}}} {{/lastSystem}}{{/countUser1}}{{{content}}}<|end_of_turn|>{{/user}}{{#assistant}}GPT4 Correct Assistant: {{{content}}}<|end_of_turn|>{{/assistant}}{{#code_user}}Code User: {{{content}}}<|end_of_turn|>{{/code_user}}{{#code_assistant}}Code Assistant: {{{content}}}<|end_of_turn|>{{/code_assistant}}{{/messages}}GPT4 Correct Assistant:",tinyllama:"{{! https://huggingface.co/TinyLlama/TinyLlama-1.1B-Chat-v1.0 }}{{#messages}}{{#system}}<|system|>\n{{{content}}}</s>\n{{/system}}{{#user}}<|user|>\n{{{content}}}</s>\n{{/user}}{{#assistant}}<|assistant|>\n{{{content}}}</s>\n{{/assistant}}{{/messages}}<|assistant|>\n",zephyr:"{{! https://huggingface.co/TheBloke/zephyr-7B-beta-AWQ#prompt-template-zephyr https://huggingface.co/HuggingFaceH4/zephyr-7b-alpha }}{{#messages}}{{#system}}<s><|system|>\n{{{content}}}</s>\n{{/system}}{{#user}}<|user|>\n{{{content}}}</s>\n{{/user}}{{#assistant}}<|assistant|>\n{{{content}}}</s>\n{{/assistant}}{{/messages}}<|assistant|>\n"},y=n.default.parse,G=n.default.render,M=(e,t)=>{if(e&&t){const s=y(t);return G(t,((e,t)=>{const s=b(e),n={},r={other:0},a={messages:[]};let o=[];for(const e of t){const t=s.includes(e.role)?e.role:"other",i={};r[e.role]||(r[e.role]=0),r[t]++,i[`count${t.charAt(0).toUpperCase()+t.slice(1)}${r[t]}`]=!0,e.role!==t&&(r[e.role]++,i[`count${e.role.charAt(0).toUpperCase()+e.role.slice(1)}${r[e.role]}`]=!0),i[t]=!0,e.role!==t&&(i[e.role]=!0);for(const e of o)i[`beforewas${e.charAt(0).toUpperCase()+e.slice(1)}`]=!0;i.content=e.content;for(const e of Object.keys(n))i[`last${e.charAt(0).toUpperCase()+e.slice(1)}`]=n[e];a[`last${t.charAt(0).toUpperCase()+t.slice(1)}`]=e.content,o=[t],n[t]=e.content,e.role!==t&&(a[`last${e.role.charAt(0).toUpperCase()+e.role.slice(1)}`]=e.content,n[e.role]=e.content,o=[t,e.role]),a.messages.push(i)}return a})(s,e))}return""},b=e=>{const t=e.filter((e=>"#"===e[0]&&"messages"===e[1]));return t.length>0?t[0][4].filter((e=>"#"===e[0]||"^"===e[0])).map((e=>e[1])):[]},F=(e,t)=>void 0===e.messages?1==e.raw||null==t.defaultPromptMessages?e.prompt:M(t.defaultPromptMessages(t.defaultContext,e.prompt),t.promptTemplateRaw||D[t.promptTemplate]):M(e.messages,t.promptTemplateRaw||D[t.promptTemplate]);TransformStream;TransformStream;const K={"text-classification":{models:["@cf/huggingface/distilbert-sst-2-int8","@cf/jpmorganchase/roberta-spam","@cf/inml/inml-roberta-dga"],class:class extends d{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{text:{type:"string",minLength:1}},required:["text"]},output:{type:"array",contentType:"application/json",items:{type:"object",properties:{score:{type:"number"},label:{type:"string"}}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.String,[e.text],{shape:[1],name:"input_text"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs=[{label:"NEGATIVE",score:e.logits.value[0][0]},{label:"POSITIVE",score:e.logits.value[0][1]}]}},baseClass:d},"text-to-image":{models:["@cf/stabilityai/stable-diffusion-xl-base-1.0","@cf/stabilityai/stable-diffusion-xl-turbo","@cf/runwayml/stable-diffusion-v1-5-inpainting","@cf/runwayml/stable-diffusion-v1-5-img2img","@cf/lykon/dreamshaper-8-lcm","@cf/bytedance/stable-diffusion-xl-lightning"],class:class extends U{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{prompt:{type:"string",minLength:1},image:{type:"array",items:{type:"number"}},mask:{type:"array",items:{type:"number"}},num_steps:{type:"integer",default:20,maximum:20},strength:{type:"number",default:1},guidance:{type:"number",default:7.5}},required:["prompt"]},output:{type:"string",contentType:"image/png",format:"binary"}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){if(this.modelSettings.generateTensorsFunc)return this.modelSettings.generateTensorsFunc(e);{let t=[new A(s.String,[e.prompt],{shape:[1],name:"input_text"}),new A(s.Int32,[e.num_steps||this.schema.input.properties.num_steps.default],{shape:[1],name:"num_steps"})];return e.image&&(t=[...t,new A(s.String,[""],{shape:[1],name:"negative_prompt"}),new A(s.Float32,[e.strength||this.schema.input.properties.strength.default],{shape:[1],name:"strength"}),new A(s.Float32,[e.guidance||this.schema.input.properties.guidance.default],{shape:[1],name:"guidance"}),new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"image"})]),e.mask&&(t=[...t,new A(s.Uint8,e.mask,{shape:[1,e.mask.length],name:"mask_image"})]),t}}OldgenerateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):e.image&&e.mask?[new A(s.String,[e.prompt],{shape:[1],name:"input_text"}),new A(s.String,[""],{shape:[1],name:"negative_prompt"}),new A(s.Int32,[e.num_steps||20],{shape:[1],name:"num_steps"}),new A(s.Float32,[e.strength||1],{shape:[1],name:"strength"}),new A(s.Float32,[e.guidance||7.5],{shape:[1],name:"guidance"}),new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"image"}),new A(s.Uint8,e.mask,{shape:[1,e.mask.length],name:"mask_image"})]:e.image?[new A(s.String,[e.prompt],{shape:[1],name:"input_text"}),new A(s.String,[""],{shape:[1],name:"negative_prompt"}),new A(s.Float32,[e.strength||1],{shape:[1],name:"strength"}),new A(s.Float32,[e.guidance||7.5],{shape:[1],name:"guidance"}),new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"image"}),new A(s.Int32,[e.num_steps||20],{shape:[1],name:"num_steps"})]:[new A(s.String,[e.prompt],{shape:[1],name:"input_text"}),new A(s.Int32,[e.num_steps||20],{shape:[1],name:"num_steps"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs=new Uint8Array(e.output_image.value)}},baseClass:U},"sentence-similarity":{models:["@hf/sentence-transformers/all-minilm-l6-v2"],class:class extends h{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{source:{type:"string",minLength:1},sentences:{type:"array",items:{type:"string",minLength:1}}},required:["source","sentences"]},output:{type:"array",contentType:"application/json",items:{type:"number"}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.String,[e.source],{shape:[1],name:"source_sentence"}),new A(s.String,e.sentences,{shape:[e.sentences.length],name:"sentences"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs=e.scores.value}},baseClass:h},"text-embeddings":{models:["@cf/baai/bge-small-en-v1.5","@cf/baai/bge-base-en-v1.5","@cf/baai/bge-large-en-v1.5"],class:class extends B{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{text:{oneOf:[{type:"string",minLength:1},{type:"array",items:{type:"string",minLength:1},maxItems:100}]}},required:["text"]},output:{type:"object",contentType:"application/json",properties:{shape:{type:"array",items:{type:"number"}},data:{type:"array",items:{type:"array",items:{type:"number"}}}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.String,Array.isArray(e.text)?e.text:[e.text],{shape:[Array.isArray(e.text)?e.text.length:[e.text].length],name:"input_text"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs={shape:e.embeddings.shape,data:e.embeddings.value}}},baseClass:B},"speech-recognition":{models:["@cf/openai/whisper","@cf/openai/whisper-tiny-en","@cf/openai/whisper-sherpa"],class:class extends P{modelSettings;preProcessedInputs;tensors;schema={input:{oneOf:[{type:"string",format:"binary"},{type:"object",properties:{audio:{type:"array",items:{type:"number"}}},required:["audio"]}]},output:{type:"object",contentType:"application/json",properties:{text:{type:"string"},word_count:{type:"number"},words:{type:"array",items:{type:"object",properties:{word:{type:"string"},start:{type:"number"},end:{type:"number"}}}},vtt:{type:"string"}},required:["text"]}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.Uint8,e.audio,{shape:[1,e.audio.length],name:"audio"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs={text:e.name.value[0].trim()}}},baseClass:P},"image-classification":{models:["@cf/microsoft/resnet-50"],class:class extends g{modelSettings;preProcessedInputs;tensors;schema={input:{oneOf:[{type:"string",format:"binary"},{type:"object",properties:{image:{type:"array",items:{type:"number"}}},required:["image"]}]},output:{type:"array",contentType:"application/json",items:{type:"object",properties:{score:{type:"number"},label:{type:"string"}}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"input"})]}postProcessing(e){if(this.modelSettings.postProcessingFunc)this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs);else{const t=[],s=e.output.value[0];for(const e in s)t.push({label:S[e],score:s[e]});t.sort(((e,t)=>t.score-e.score)),this.postProcessedOutputs=t.slice(0,5)}}},baseClass:g},"object-detection":{models:["@cf/facebook/detr-resnet-50"],class:class extends L{modelSettings;preProcessedInputs;tensors;schema={input:{oneOf:[{type:"string",format:"binary"},{type:"object",properties:{image:{type:"array",items:{type:"number"}}}}]},output:{type:"array",contentType:"application/json",items:{type:"object",properties:{score:{type:"number"},label:{type:"string"},box:{type:"object",properties:{xmin:{type:"number"},ymin:{type:"number"},xmax:{type:"number"},ymax:{type:"number"}}}}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"input"})]}postProcessing(e){if(this.modelSettings.postProcessingFunc)this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs);else{const t=e.scores.value[0].map(((t,s)=>({score:t,label:e.name.value[e.labels.value[0][s]],box:{xmin:e.boxes.value[0][s][0],ymin:e.boxes.value[0][s][1],xmax:e.boxes.value[0][s][2],ymax:e.boxes.value[0][s][3]}})));this.postProcessedOutputs=t.sort(((e,t)=>t.score-e.score))}}},baseClass:L},"text-generation":{models:["@cf/meta/llama-3-8b-instruct","@cf/meta/llama-2-7b-chat-int8","@cf/mistral/mistral-7b-instruct-v0.1","@cf/mistral/mistral-7b-instruct-v0.1-vllm","@cf/mistral/mistral-7b-instruct-v0.2-lora","@cf/meta/llama-2-7b-chat-fp16","@hf/thebloke/llama-2-13b-chat-awq","@hf/thebloke/zephyr-7b-beta-awq","@hf/thebloke/mistral-7b-instruct-v0.1-awq","@hf/thebloke/codellama-7b-instruct-awq","@hf/thebloke/openchat_3.5-awq","@hf/thebloke/openhermes-2.5-mistral-7b-awq","@hf/thebloke/neural-chat-7b-v3-1-awq","@hf/thebloke/llamaguard-7b-awq","@hf/thebloke/deepseek-coder-6.7b-base-awq","@hf/thebloke/deepseek-coder-6.7b-instruct-awq","@hf/nousresearch/hermes-2-pro-mistral-7b","@hf/mistral/mistral-7b-instruct-v0.2","@cf/mistral/mixtral-8x7b-instruct-v0.1-awq","@hf/google/gemma-7b-it","@hf/nexusflow/starling-lm-7b-beta","@cf/deepseek-ai/deepseek-math-7b-instruct","@cf/defog/sqlcoder-7b-2","@cf/openchat/openchat-3.5-0106","@cf/tiiuae/falcon-7b-instruct","@cf/thebloke/discolm-german-7b-v1-awq","@cf/qwen/qwen1.5-0.5b-chat","@cf/qwen/qwen1.5-1.8b-chat","@cf/qwen/qwen1.5-7b-chat-awq","@cf/qwen/qwen1.5-14b-chat-awq","@cf/tinyllama/tinyllama-1.1b-chat-v1.0","@cf/microsoft/phi-2","@cf/google/gemma-2b-it-lora","@cf/google/gemma-7b-it-lora","@cf/meta-llama/llama-2-7b-chat-hf-lora","@cf/deepseek-ai/deepseek-coder-7b-instruct-v1.5","@cf/nexaaidev/octopus-v2","@cf/m-a-p/opencodeinterpreter-ds-6.7b","@cf/fblgit/una-cybertron-7b-v2-bf16","@cf/sven/test"],class:class extends H{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",oneOf:[{properties:{prompt:{type:"string",minLength:1,maxLength:6144},raw:{type:"boolean",default:!1},stream:{type:"boolean",default:!1},max_tokens:{type:"integer",default:256},temperature:{type:"number"},lora:{type:"string"}},required:["prompt"]},{properties:{messages:{type:"array",items:{type:"object",properties:{role:{type:"string"},content:{type:"string",maxLength:6144}},required:["role","content"]}},stream:{type:"boolean",default:!1},max_tokens:{type:"integer",default:256},temperature:{type:"number"}},required:["messages"]}]},output:{oneOf:[{type:"object",contentType:"application/json",properties:{response:{type:"string"}}},{type:"string",contentType:"text/event-stream",format:"binary"}]}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t||{experimental:!0,inputsDefaultsStream:{max_tokens:512},inputsDefaults:{max_tokens:256},preProcessingArgs:{promptTemplate:"inst",promptTemplateRaw:!1,defaultContext:""}}}preProcessing(){const e=this.modelSettings.preProcessingArgs||{};this.inputs.stream&&this.modelSettings.inputsDefaultsStream?this.inputs={...this.modelSettings.inputsDefaultsStream,...this.inputs}:this.modelSettings.inputsDefaults&&(this.inputs={...this.modelSettings.inputsDefaults,...this.inputs}),this.preProcessedInputs={prompt:F(this.inputs,e),max_tokens:this.inputs.max_tokens,temperature:this.inputs.temperature,stream:!!this.inputs.stream}}generateTensors(e){if(this.modelSettings.generateTensorsFunc)return this.modelSettings.generateTensorsFunc(e);return[new A(s.String,[e.prompt],{shape:[1],name:"INPUT_0"}),new A(s.Uint32,[e.max_tokens],{shape:[1],name:"INPUT_1"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs={response:this.modelSettings.postProcessingFunc(e,this.preProcessedInputs)}:this.postProcessedOutputs={response:e.name.value[0]}}postProcessingStream(e,t){return this.modelSettings.postProcessingFuncStream?{response:this.modelSettings.postProcessingFuncStream(e,this.preProcessedInputs,t)}:{response:e.name.value[0]}}},baseClass:H},translation:{models:["@cf/meta/m2m100-1.2b"],class:class extends f{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{text:{type:"string",minLength:1},source_lang:{type:"string",default:"en"},target_lang:{type:"string"}},required:["text","target_lang"]},output:{type:"object",contentType:"application/json",properties:{translated_text:{type:"string"}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.String,[e.text],{shape:[1,1],name:"text"}),new A(s.String,[e.source_lang||"en"],{shape:[1,1],name:"source_lang"}),new A(s.String,[e.target_lang],{shape:[1,1],name:"target_lang"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs={translated_text:e.name.value[0]}}},baseClass:f},summarization:{models:["@cf/facebook/bart-large-cnn"],class:class extends C{modelSettings;preProcessedInputs;tensors;schema={input:{type:"object",properties:{input_text:{type:"string",minLength:1},max_length:{type:"integer",default:1024}},required:["input_text"]},output:{type:"object",contentType:"application/json",properties:{summary:{type:"string"}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t}preProcessing(){this.preProcessedInputs=this.inputs}generateTensors(e){return this.modelSettings.generateTensorsFunc?this.modelSettings.generateTensorsFunc(e):[new A(s.Int32,[e.max_length||this.schema.input.properties.max_length.default],{shape:[1],name:"max_length"}),new A(s.String,[e.input_text],{shape:[1],name:"input_text"})]}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs=this.modelSettings.postProcessingFunc(e,this.preProcessedInputs):this.postProcessedOutputs={summary:e.name.value[0]}}},baseClass:C},"image-to-text":{models:["@cf/unum/uform-gen2-qwen-500m","@cf/llava-hf/llava-1.5-7b-hf"],class:class extends N{modelSettings;preProcessedInputs;tensors;schema={input:{oneOf:[{type:"string",format:"binary"},{type:"object",properties:{temperature:{type:"number"},prompt:{type:"string"},raw:{type:"boolean",default:!1},messages:{type:"array",items:{type:"object",properties:{role:{type:"string"},content:{type:"string",maxLength:6144}},required:["role","content"]}},image:{oneOf:[{type:"array",items:{type:"number"}},{type:"string",format:"binary"}]},max_tokens:{type:"integer",default:512}},required:["image"],not:{required:["prompt","messages"]},errorMessage:{not:'"prompt" and "messages" are mutually exclusive'}}]},output:{type:"object",contentType:"application/json",properties:{description:{type:"string"}}}};constructor(e,t){super(),this.inputs=e,this.modelSettings=t||{}}preProcessing(){const e=this.modelSettings.preProcessingArgs||{};this.inputs={...this.modelSettings.inputsDefaults,...this.inputs},this.preProcessedInputs={prompt:F(this.inputs,e),image:this.inputs.image,max_tokens:this.inputs.max_tokens,temperature:this.inputs.temperature}}generateTensors(e){if(this.modelSettings.generateTensorsFunc)return this.modelSettings.generateTensorsFunc(e);{let t=[new A(s.Int32,[e.max_tokens||this.schema.input.oneOf.filter((e=>"object"==e.type))[0].properties.max_tokens.default],{shape:[1],name:"max_tokens"}),new A(s.String,[e.prompt],{shape:[1],name:"prompt"}),new A(s.Uint8,e.image,{shape:[1,e.image.length],name:"image"})];return e.temperature&&(t=[...t,new A(s.Float32,[e.temperature],{shape:[1],name:"temperature"})]),t}}postProcessing(e){this.modelSettings.postProcessingFunc?this.postProcessedOutputs={description:this.modelSettings.postProcessingFunc(e,this.preProcessedInputs)}:this.postProcessedOutputs={description:e.name.value[0]}}},baseClass:N}};T("deepseek",c,["<|EOT|>"]),T("bare",c),T("inst",R),T("openchat",R),T("chatml",R,["<|im_end|>"]),T("orca-hashes",R),T("llama2",R),T("zephyr",R),T("mistral-instruct",R),T("mistral-instruct",R),T("gemma",R),T("hermes2-pro",R),T("starling",R),T("llama2",c),I({preProcessingArgs:{promptTemplate:"phi-2",defaultPromptMessages:(e,t)=>[{role:"question",content:t}]}}),I({preProcessingArgs:{promptTemplate:"sqlcoder"}}),I({preProcessingArgs:{defaultContext:""}}),I({preProcessingArgs:{promptTemplate:"falcon"}}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({preProcessingArgs:{promptTemplate:"tinyllama"}}),I({preProcessingArgs:{promptTemplate:"openchat-alt"}}),I({preProcessingArgs:{promptTemplate:"gemma"}}),I({preProcessingArgs:{promptTemplate:"gemma"}}),I({preProcessingArgs:{promptTemplate:"mistral-instruct"}}),I({experimental:!0,preProcessingArgs:{promptTemplate:"mistral-instruct"}}),I({preProcessingArgs:{promptTemplate:"llama2"}}),I({experimental:!0,inputsDefaultsStream:{max_tokens:1800},inputsDefaults:{max_tokens:256},preProcessingArgs:{promptTemplate:"mistral-instruct"}}),I({preProcessingArgs:{promptTemplate:"llama3"}}),I({experimental:!0}),I({experimental:!0}),I({preProcessingArgs:{promptTemplate:"chatml"}}),I({experimental:!0}),l({inputsDefaultsStream:{max_tokens:2500},preProcessingArgs:{promptTemplate:"llama2"}}),l({preProcessingArgs:{promptTemplate:"llama2"}}),l({preProcessingArgs:{promptTemplate:"mistral-instruct"}});class _ extends Error{httpCode;constructor(e,t){super(e),this.name="InferenceUpstreamError",this.httpCode=t}}exports.Ai=class{binding;options;logs;lastRequestId;constructor(e,t={}){if(!e)throw new Error("Ai binding is undefined. Please provide a valid binding.");this.binding=e,this.options=t,this.lastRequestId=""}async run(e,t){const s=await this.binding.run(e,t,this.options);return this.lastRequestId=this.binding.lastRequestId,this.options.debug&&(this.logs=this.binding.getLogs()),s}getLogs(){return this.logs}},exports.InferenceUpstreamError=_,exports.modelAliases={"@hf/mistral/mistral-7b-instruct-v0.2":["@hf/mistralai/mistral-7b-instruct-v0.2"],"@cf/meta/llama-3-8b-instruct":["@hf/meta-llama/meta-llama-3-8b-instruct"],"@cf/mistral/mistral-7b-instruct-v0.1-vllm":["@cf/mistral/mistral-7b-instruct-v0.1"]},exports.modelMappings=K;
