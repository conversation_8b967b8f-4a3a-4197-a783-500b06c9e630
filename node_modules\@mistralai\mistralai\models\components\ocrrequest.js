"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRRequest$ = exports.OCRRequest$outboundSchema = exports.OCRRequest$inboundSchema = exports.Document$ = exports.Document$outboundSchema = exports.Document$inboundSchema = void 0;
exports.documentToJSON = documentToJSON;
exports.documentFromJSON = documentFromJSON;
exports.ocrRequestToJSON = ocrRequestToJSON;
exports.ocrRequestFromJSON = ocrRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const documenturlchunk_js_1 = require("./documenturlchunk.js");
const imageurlchunk_js_1 = require("./imageurlchunk.js");
/** @internal */
exports.Document$inboundSchema = z.union([imageurlchunk_js_1.ImageURLChunk$inboundSchema, documenturlchunk_js_1.DocumentURLChunk$inboundSchema]);
/** @internal */
exports.Document$outboundSchema = z.union([imageurlchunk_js_1.ImageURLChunk$outboundSchema, documenturlchunk_js_1.DocumentURLChunk$outboundSchema]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Document$;
(function (Document$) {
    /** @deprecated use `Document$inboundSchema` instead. */
    Document$.inboundSchema = exports.Document$inboundSchema;
    /** @deprecated use `Document$outboundSchema` instead. */
    Document$.outboundSchema = exports.Document$outboundSchema;
})(Document$ || (exports.Document$ = Document$ = {}));
function documentToJSON(document) {
    return JSON.stringify(exports.Document$outboundSchema.parse(document));
}
function documentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Document$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Document' from JSON`);
}
/** @internal */
exports.OCRRequest$inboundSchema = z.object({
    model: z.nullable(z.string()),
    id: z.string().optional(),
    document: z.union([
        imageurlchunk_js_1.ImageURLChunk$inboundSchema,
        documenturlchunk_js_1.DocumentURLChunk$inboundSchema,
    ]),
    pages: z.nullable(z.array(z.number().int())).optional(),
    include_image_base64: z.nullable(z.boolean()).optional(),
    image_limit: z.nullable(z.number().int()).optional(),
    image_min_size: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "include_image_base64": "includeImageBase64",
        "image_limit": "imageLimit",
        "image_min_size": "imageMinSize",
    });
});
/** @internal */
exports.OCRRequest$outboundSchema = z.object({
    model: z.nullable(z.string()),
    id: z.string().optional(),
    document: z.union([
        imageurlchunk_js_1.ImageURLChunk$outboundSchema,
        documenturlchunk_js_1.DocumentURLChunk$outboundSchema,
    ]),
    pages: z.nullable(z.array(z.number().int())).optional(),
    includeImageBase64: z.nullable(z.boolean()).optional(),
    imageLimit: z.nullable(z.number().int()).optional(),
    imageMinSize: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        includeImageBase64: "include_image_base64",
        imageLimit: "image_limit",
        imageMinSize: "image_min_size",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var OCRRequest$;
(function (OCRRequest$) {
    /** @deprecated use `OCRRequest$inboundSchema` instead. */
    OCRRequest$.inboundSchema = exports.OCRRequest$inboundSchema;
    /** @deprecated use `OCRRequest$outboundSchema` instead. */
    OCRRequest$.outboundSchema = exports.OCRRequest$outboundSchema;
})(OCRRequest$ || (exports.OCRRequest$ = OCRRequest$ = {}));
function ocrRequestToJSON(ocrRequest) {
    return JSON.stringify(exports.OCRRequest$outboundSchema.parse(ocrRequest));
}
function ocrRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.OCRRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'OCRRequest' from JSON`);
}
//# sourceMappingURL=ocrrequest.js.map