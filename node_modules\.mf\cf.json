{"clientTcpRtt": 14, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 17451, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "ID", "region": "Central Java", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "rdk0RfBPDibbOiXMT5JnNIdDp8ls27kzlwR8Ar+d4I8=", "tlsExportedAuthenticator": {"clientFinished": "b0b5a295181a9d729fc7177f31c25c6c5e5c54547a498903aacf6b1f1ed05aa6229c1309a13a9bc879c9aa6f49f73abc", "clientHandshake": "73ec4b7336c5f15381d7688f6ee789b8d14c43dfcae55164f2a9882af282b2e944456a5198ab985b07234b5b7de956cf", "serverHandshake": "db833826f8d7b0e9b93fe6e9c08033721322aa4ca46da4a8d758b3edd5531af1fb6f390c19e84a2a0db3145d921aaa77", "serverFinished": "0d105ec362165cf0dacf9172637b2f04c3a66fc649d10e3e1839e161d82a4dc2180fefea01971eb575fad757b7ad4531"}, "tlsClientHelloLength": "386", "colo": "CGK", "timezone": "Asia/Jakarta", "longitude": "110.47530", "latitude": "-7.27220", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "50148", "city": "Semarang", "tlsVersion": "TLSv1.3", "regionCode": "JT", "asOrganization": "Biznet Networks", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}