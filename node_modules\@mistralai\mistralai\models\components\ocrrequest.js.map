{"version": 3, "file": "ocrrequest.js", "sourceRoot": "", "sources": ["../../src/models/components/ocrrequest.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFH,wCAEC;AAED,4CAQC;AA0ED,4CAEC;AAED,gDAQC;AAlLD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAGjD,+DAK+B;AAC/B,yDAK4B;AAgC5B,gBAAgB;AACH,QAAA,sBAAsB,GAI/B,CAAC,CAAC,KAAK,CAAC,CAAC,8CAA2B,EAAE,oDAA8B,CAAC,CAAC,CAAC;AAO3E,gBAAgB;AACH,QAAA,uBAAuB,GAIhC,CAAC,CAAC,KAAK,CAAC,CAAC,+CAA4B,EAAE,qDAA+B,CAAC,CAAC,CAAC;AAE7E;;;GAGG;AACH,IAAiB,SAAS,CAOzB;AAPD,WAAiB,SAAS;IACxB,wDAAwD;IAC3C,uBAAa,GAAG,8BAAsB,CAAC;IACpD,yDAAyD;IAC5C,wBAAc,GAAG,+BAAuB,CAAC;AAGxD,CAAC,EAPgB,SAAS,yBAAT,SAAS,QAOzB;AAED,SAAgB,cAAc,CAAC,QAAkB;IAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,+BAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAgB,gBAAgB,CAC9B,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,8BAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAClD,sCAAsC,CACvC,CAAC;AACJ,CAAC;AAED,gBAAgB;AACH,QAAA,wBAAwB,GAIjC,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC7B,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzB,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QAChB,8CAA2B;QAC3B,oDAA8B;KAC/B,CAAC;IACF,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvD,oBAAoB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE;IACxD,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpD,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACxD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,sBAAsB,EAAE,oBAAoB;QAC5C,aAAa,EAAE,YAAY;QAC3B,gBAAgB,EAAE,cAAc;KACjC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAaH,gBAAgB;AACH,QAAA,yBAAyB,GAIlC,CAAC,CAAC,MAAM,CAAC;IACX,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC7B,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzB,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QAChB,+CAA4B;QAC5B,qDAA+B;KAChC,CAAC;IACF,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvD,kBAAkB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtD,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACnD,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACtD,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,kBAAkB,EAAE,sBAAsB;QAC1C,UAAU,EAAE,aAAa;QACzB,YAAY,EAAE,gBAAgB;KAC/B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,WAAW,CAO3B;AAPD,WAAiB,WAAW;IAC1B,0DAA0D;IAC7C,yBAAa,GAAG,gCAAwB,CAAC;IACtD,2DAA2D;IAC9C,0BAAc,GAAG,iCAAyB,CAAC;AAG1D,CAAC,EAPgB,WAAW,2BAAX,WAAW,QAO3B;AAED,SAAgB,gBAAgB,CAAC,UAAsB;IACrD,OAAO,IAAI,CAAC,SAAS,CAAC,iCAAyB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACrE,CAAC;AAED,SAAgB,kBAAkB,CAChC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,gCAAwB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACpD,wCAAwC,CACzC,CAAC;AACJ,CAAC"}