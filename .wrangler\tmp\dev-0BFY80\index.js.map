{"version": 3, "sources": ["../bundle-aSfhnm/checked-fetch.js", "../../../src/agents/product_audience_matching.ts", "../../../src/agents/marketing_analysis.ts", "../../../src/agents/context_planner_image_generator.ts", "../../../src/agents/simplified_digital_brochure_prompt_generator.ts", "../../../src/workflow/task_workflow.ts", "../../../src/index.ts", "file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/32026684e21afda6/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../bundle-aSfhnm/middleware-insertion-facade.js", "file:///C:/Users/<USER>/AppData/Local/npm-cache/_npx/32026684e21afda6/node_modules/wrangler/templates/middleware/common.ts", "../bundle-aSfhnm/middleware-loader.entry.ts"], "sourceRoot": "E:\\backup-HD-lama\\AI\\agent-cloudflare\\.wrangler\\tmp\\dev-0BFY80", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "const systemPrompt = `Use this persona as an expert product-audience matching specialist in a multi-agent system for Facebook ad generation. Your task is to analyze how specific audience segments align with the product features and determine the most effective conversion strategies for Facebook advertising.\r\n# Input Context\r\n- Structured JSON data from Marketing Analysis Framework\r\n\r\n# Methodology\r\n## Value Proposition Alignment\r\n- Map product benefits to audience needs with alignment scores (1-10)\r\n- Assess price-to-value perception\r\n- Calculate overall value proposition score\r\n\r\n## Engagement Strategy\r\n- Identify key engagement factors and barriers\r\n- Develop audience-specific engagement approaches\r\n- Consider Facebook-specific consumption patterns\r\n\r\n## Conversion Strategy\r\n- Identify decision factors and friction points\r\n- Recommend conversion accelerators\r\n- Design optimal conversion path\r\n\r\n## Competitive Positioning\r\n- Identify differentiation factors\r\n- Assess competitive advantage with specific audience\r\n- Score competitive position strength\r\n\r\n## Marketing Recommendations\r\n- Define primary message focus\r\n- Prioritize key benefits\r\n- Recommend tone, visuals, and CTA strategy\r\n\r\n# Behavioral Instructions\r\n- Prioritize finding strongest product-audience fit\r\n- Focus on actionable, tactical recommendations\r\n- Apply Facebook ad best practices\r\n- Consider both explicit and implicit alignment factors\r\n- Provide evidence-based match assessments\r\n\r\n## Output Format\r\n\r\nExample output:\r\n{\r\n  \"product_audience_matches\": [\r\n    {\r\n      \"segment_id\": \"string\",\r\n      \"segment_name\": \"string\",\r\n      \"value_proposition_alignment\": {\r\n        \"benefit_alignment\": [\r\n          {\r\n            \"product_benefit\": \"string\",\r\n            \"audience_need\": \"string\",\r\n            \"alignment_strength\": 0\r\n          }\r\n        ],\r\n        \"price_value_perception\": {\r\n          \"score\": 0,\r\n          \"key_factors\": [\"string\"]\r\n        },\r\n        \"overall_value_score\": 0\r\n      },\r\n      \"engagement_potential\": {\r\n        \"key_engagement_factors\": [\"string\"],\r\n        \"engagement_barriers\": [\"string\"],\r\n        \"overall_engagement_score\": 0\r\n      },\r\n      \"conversion_strategy\": {\r\n        \"key_decision_factors\": [\"string\"],\r\n        \"friction_points\": [\"string\"],\r\n        \"conversion_accelerators\": [\"string\"],\r\n        \"conversion_path\": \"string\"\r\n      },\r\n      \"competitive_position\": {\r\n        \"differentiation_factors\": [\"string\"],\r\n        \"competitive_position_score\": 0\r\n      },\r\n      \"marketing_recommendations\": {\r\n        \"primary_message\": \"string\",\r\n        \"key_benefits\": [\"string\"],\r\n        \"tone_and_visuals\": \"string\",\r\n        \"cta_strategy\": \"string\"\r\n      },\r\n      \"overall_match_score\": 0\r\n    }\r\n  ],\r\n  \"segment_prioritization\": {\r\n    \"primary_segment_id\": \"string\",\r\n    \"prioritization_reasoning\": \"string\"\r\n  }\r\n}\r\n\r\n`;\r\n\r\nexport default {\r\n  async fetch(request: Request, env: any) {\r\n    if (request.method !== \"POST\") {\r\n      return new Response(\"Method Not Allowed\", { status: 405 });\r\n    }\r\n\r\n    const input = await request.json();\r\n\r\n    const messages: any[] = [\r\n      {\r\n        role: \"system\",\r\n        content: systemPrompt\r\n      },\r\n    ];\r\n\r\n    if (input.logo) {\r\n      messages.push({\r\n        role: \"user\",\r\n        content: [\r\n          { type: \"text\", text: \"Berikut logo brand untuk referensi:\" },\r\n          { type: \"image_url\", image_url: { url: input.logo } }\r\n        ]\r\n      });\r\n    }\r\n\r\n    if (input.product) {\r\n      messages.push({\r\n        role: \"user\",\r\n        content: [\r\n          { type: \"text\", text: \"Berikut gambar produk untuk referensi:\" },\r\n          { type: \"image_url\", image_url: { url: input.product } }\r\n        ]\r\n      });\r\n    }\r\n\r\n    const finalPrompt = JSON.stringify(input, null, 2)\r\n\r\n    messages.push({\r\n      role: \"user\",\r\n      content: finalPrompt\r\n    });\r\n\r\n    console.log(\"messages prompt product audience matching\", JSON.stringify(messages))\r\n    const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${env.OPENAI_API_KEY}`,\r\n      },\r\n      body: JSON.stringify({\r\n        model: \"gpt-4o-mini\",\r\n        messages,\r\n        temperature: 0.6,\r\n      }),\r\n    });\r\n\r\n    const result = await response.json();\r\n    // Ambil hanya konten JSON dari output OpenAI\r\n    let jsonContent = null;\r\n    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {\r\n      let content = result.choices[0].message.content;\r\n      // Hapus blok kode markdown jika ada\r\n      if (typeof content === 'string') {\r\n        content = content.trim();\r\n        if (content.startsWith('```json')) {\r\n          content = content.replace(/^```json\\s*/i, '').replace(/```\\s*$/i, '');\r\n        } else if (content.startsWith('```')) {\r\n          content = content.replace(/^```\\s*/i, '').replace(/```\\s*$/i, '');\r\n        }\r\n      }\r\n      try {\r\n        jsonContent = JSON.parse(content);\r\n      } catch (e) {\r\n        // Jika gagal parse, fallback ke raw content\r\n        jsonContent = content;\r\n      }\r\n    }\r\n    return new Response(JSON.stringify(jsonContent), {\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    });\r\n  },\r\n};\r\n", "const systemPrompt = `You are an expert marketing analyst specializing in product-audience matching for digital marketing. Your role is to analyze product details, identify the optimal audience segment, and develop targeted marketing strategy recommendations.\r\n## Methodology\r\n### Input Processing\r\n- Extract product details from 'product_description' and 'offer'\r\n- Identify regional market context from 'target_country'\r\n- Apply visual styling parameters when available ('logo', 'main_color', 'visual_style')\r\n- Standardize terminology for consistent analysis\r\n\r\n### Analysis Workflow\r\n1. **Product Categorization**\r\n   - Map to standard industry categories and subcategories\r\n   - Identify price positioning and offer structure\r\n   - Extract key features and competitive differentiators\r\n\r\n2. **Audience Identification**\r\n   - Apply market segmentation principles to identify optimal audience\r\n   - Map product attributes to audience needs and preferences\r\n   - Consider cultural and regional market factors\r\n   - Evaluate product-audience fit strength\r\n\r\n3. **Marketing Strategy Development**\r\n   - Define core messaging strategy based on product-audience alignment\r\n   - Develop communication approach tailored to audience preferences\r\n   - Create visual strategy recommendations aligned with product positioning\r\n   - Generate conversion-focused call-to-action recommendations\r\n\r\n### Validation\r\n- Verify internal consistency between product features and audience needs\r\n- Ensure cultural relevance for target country\r\n- Confirm strategic alignment between product positioning and marketing recommendations\r\n\r\n## Theoretical Framework\r\n### Product Analysis Theory\r\n- Apply standard industry classification systems for categorization\r\n- Use feature-benefit-value chain analysis for proposition development\r\n- Employ premium vs. value positioning models for market positioning\r\n\r\n### Audience Segmentation Theory\r\n- Utilize demographic profiling based on age-cohort patterns and gender preferences\r\n- Apply psychographic models including values, pain points, and aspirations\r\n- Incorporate behavioral segmentation through purchase patterns and digital behavior\r\n\r\n### Marketing Strategy Theory\r\n- Implement benefit-focused communication and problem-solution messaging\r\n- Apply communication style theories including tone alignment and cultural patterns\r\n- Utilize color psychology and design effectiveness principles\r\n- Follow CTA effectiveness and decision psychology frameworks\r\n- CTA only Get Voucher and Redeem Voucher\r\n\r\n## Analysis Requirements\r\n### Product Analysis\r\n1. **Category & Subcategory**\r\n   - Identify the appropriate industry category and subcategory\r\n   - Ensure classification aligns with standard marketing taxonomies\r\n\r\n2. **Pricing Analysis**\r\n   - Extract price points from the offer\r\n   - Calculate discount percentage when applicable\r\n   - Identify payment terms and currency\r\n\r\n3. **Feature Identification**\r\n   - Extract 3-5 key product features\r\n   - Identify 2-3 unique selling points\r\n   - Determine market positioning (premium, mid-market, value)\r\n\r\n### Audience Segmentation\r\n1. **Segment Identification**\r\n   - Generate a unique segment_id (format: S-XXX-YYY where XXX is category code and YYY is sequential)\r\n   - Create a descriptive segment name that clearly identifies the target audience\r\n\r\n2. **Demographic Profiling**\r\n   - Define the age range most likely to respond to the offer\r\n   - Identify gender skew if applicable\r\n   - Determine income level alignment\r\n   - Specify location types (urban, suburban, rural)\r\n   - location_target: taken from target_country if it exists, otherwise customize it\r\n\r\n3. **Psychographic Analysis**\r\n   - Identify 3-5 core values held by the target audience\r\n   - Determine 3-5 key pain points addressed by the product\r\n   - Define 3-5 aspirations that motivate purchase behavior\r\n\r\n4. **Behavioral Analysis**\r\n   - Identify 5-7 key behavioral patterns, including:\r\n     - Purchase patterns\r\n     - Online activities\r\n     - Platform usage habits\r\n     - Decision-making behaviors\r\n\r\n5. **Interest Identification**\r\n   - Identify 5-7 primary interests relevant to the product category\r\n\r\n### Marketing Strategy Development\r\n1. **Messaging Framework**\r\n   - Create a concise primary message (under 20 words)\r\n   - Identify 3-5 key benefits to emphasize\r\n   - Develop communication style recommendations:\r\n     - Tone (informative, persuasive, empathetic, humorous, etc.)\r\n     - Formality level (1-10 scale)\r\n     - Emotional appeal level (1-10 scale)\r\n\r\n2. **Visual Strategy**\r\n   - Recommend color palette (include the provided main_color if available)\r\n   - Suggest imagery style\r\n   - Recommend design approach aligned with product positioning and visual_style preference\r\n\r\n3. **Conversion Strategy**\r\n   - Provide 2-3 specific call-to-action recommendations\r\n\r\n## Rules\r\n1. Analyze only the provided inputs without requiring intermediate reasoning steps\r\n2. Use provided optional inputs when available\r\n3. Set reasonable values when information is uncertain\r\n4. Ensure all recommendations are culturally appropriate for the target country\r\n5. Provide specific, actionable insights rather than generic advice\r\n6. Return ONLY the JSON output with no surrounding text\r\n\r\n## Output Format\r\nYou must provide output ONLY in the following JSON format, with no additional text or commentary:\r\n\r\nExample output:\r\n{\r\n  \"product_analysis\": {\r\n    \"category\": \"string\",\r\n    \"subcategory\": \"string\",\r\n    \"pricing\": {\r\n      \"base_price\": \"string\",\r\n      \"discounted_price\": \"string\",\r\n      \"discount_percentage\": \"string\",\r\n      \"currency\": \"string\"\r\n    },\r\n    \"key_features\": [\"string\", \"string\", ...],\r\n    \"unique_selling_points\": [\"string\", \"string\", ...],\r\n    \"market_position\": \"string\"\r\n  },\r\n  \"audience_segment\": {\r\n    \"segment_id\": \"string\",\r\n    \"segment_name\": \"string\",\r\n    \"demographics\": {\r\n      \"age_range\": \"string\",\r\n      \"gender_skew\": \"string\",\r\n      \"income_level\": \"string\",\r\n      \"location_type\": \"string\",\r\n      \"location_target\": \"string\"\r\n    },\r\n    \"psychographics\": {\r\n      \"values\": [\"string\", \"string\", ...],\r\n      \"pain_points\": [\"string\", \"string\", ...],\r\n      \"aspirations\": [\"string\", \"string\", ...]\r\n    },\r\n    \"behaviors\": [\"string\", \"string\", ...],\r\n    \"interests\": [\"string\", \"string\", ...]\r\n  },\r\n  \"marketing_strategy\": {\r\n    \"primary_message\": \"string\",\r\n    \"key_benefits\": [\"string\", \"string\", ...],\r\n    \"communication_style\": {\r\n      \"tone\": \"string\",\r\n      \"formality_level\": 0,\r\n      \"emotional_appeal\": 0\r\n    },\r\n    \"visual_strategy\": {\r\n      \"color_palette\": [\"string\", \"string\", ...],\r\n      \"imagery_style\": \"string\",\r\n      \"design_approach\": \"string\"\r\n    },\r\n    \"cta_recommendations\": [\"Get Voucher\", \"Voucher Redeem\"]\r\n  }\r\n}\r\n\r\nHere is the input data:\r\n<input>`;\r\n\r\nexport default {\r\n  async fetch(request: Request, env: any) {\r\n    console.log(\"Marketing agent dipanggil\")\r\n\r\n    if (request.method !== \"POST\") {\r\n      return new Response(\"Method Not Allowed\", { status: 405 })\r\n    }\r\n\r\n    try {\r\n      const input = await request.json()\r\n      console.log(\"Input diterima:\", input)\r\n\r\n      // Build messages array\r\n      const messages: any[] = [\r\n        {\r\n          role: \"system\",\r\n          content: systemPrompt\r\n        },\r\n      ]\r\n\r\n      // Tambah logo kalau ada\r\n      if (input.logo) {\r\n        messages.push({\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: \"Berikut logo brand untuk referensi:\" },\r\n            { type: \"image_url\", image_url: { url: input.logo } }\r\n          ]\r\n        })\r\n      }\r\n\r\n      // Tambah gambar produk kalau ada\r\n      if (input.product) {\r\n        messages.push({\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: \"Berikut gambar produk untuk referensi:\" },\r\n            { type: \"image_url\", image_url: { url: input.product } }\r\n          ]\r\n        })\r\n      }\r\n\r\n      // Generate final prompt text dari template systemPrompt\r\n      const finalPrompt = JSON.stringify(input, null, 2)\r\n\r\n      // Tambah prompt utama\r\n      messages.push({\r\n        role: \"user\",\r\n        content: finalPrompt\r\n      })\r\n\r\n      console.log(\"messages prompt marketing analysis\", JSON.stringify(messages))\r\n      // Kirim ke OpenAI API\r\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${env.OPENAI_API_KEY}`,\r\n        },\r\n        body: JSON.stringify({\r\n          model: \"gpt-4o\",\r\n          messages,\r\n          temperature: 0.7,\r\n        }),\r\n      })\r\n\r\n      const result = await response.json()\r\n      //console.log(\"Hasil OpenAI:\", result)\r\n\r\n      // Ambil konten JSON dari output OpenAI\r\n      let jsonContent = null\r\n      if (result?.choices?.[0]?.message?.content) {\r\n        let content = result.choices[0].message.content.trim()\r\n\r\n        // Hapus blok ```json atau ```\r\n        if (content.startsWith(\"```json\")) {\r\n          content = content.replace(/^```json\\s*/i, '').replace(/```\\s*$/i, '')\r\n        } else if (content.startsWith(\"```\")) {\r\n          content = content.replace(/^```\\s*/i, '').replace(/```\\s*$/i, '')\r\n        }\r\n\r\n        try {\r\n          jsonContent = JSON.parse(content)\r\n        } catch (e) {\r\n          console.warn(\"Gagal parse JSON, fallback ke string.\")\r\n          jsonContent = content\r\n        }\r\n      }\r\n\r\n      return new Response(JSON.stringify(jsonContent), {\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n      })\r\n\r\n    } catch (e: any) {\r\n      console.error(\"Error di marketing agent:\", e)\r\n      return new Response(\"Internal Server Error: \" + e.message, { status: 500 })\r\n    }\r\n  },\r\n}", "const systemPrompt = `You are a context planner that processes marketing Analysis and product-audience matching data to create strategic guidance for marketing brochure design. Your goal is to synthesize information into a comprehensive design brief that drives click-through rates.\r\n# Input Processing:\r\nExtract key insights from:\r\n1. Product features, pricing, and positioning\r\n2. Target audience demographics and psychographics\r\n3. Marketing strategy alignment\r\n4. Conversion optimization factors\r\n\r\n# Response Format:\r\nExample output:\r\n{\r\n  \"context_planner\": \"string - comprehensive design strategy context\"\r\n}\r\n\r\n# Design Context Requirements:\r\nYour response must include guidance on:\r\n- Visual hierarchy and layout priorities\r\n- Color palette and aesthetic direction (using colors from analysis)\r\n- Typography and imagery style (minimal text)\r\n- Strategic CTA positioning with EITHER \"Get Voucher\" OR \"Redeem Voucher\" (not both), - ALWAYS KEEP THE CTA TEXT IN ENGLISH\r\n- Trust elements and urgency triggers\r\n- Mobile-first considerations for Instagram audience\r\n- Elements that drive immediate engagement and action\r\n- Styling elegant brochures with conversion-focused design\r\n- Background image customized with product theme\r\n- Circular product showcase center-stage\r\n- Premium feel with value emphasis\r\n\r\n# Language Requirements:\r\n- The design strategy context should be written in English\r\n- The CTA button must always be in English, using EITHER \"GET VOUCHER\" OR \"REDEEM VOUCHER\" (select one)\r\n- Any other text elements that would appear in the final brochure/banner (headlines, descriptions, etc.) MUST be in the target country's language (e.g., Bahasa Indonesia for Indonesia)\r\n- Provide English translations in parentheses after any non-English text examples\r\n\r\n# Special Handling Instructions:\r\n1. Sensitive Products Handling\r\n - For products/services in sensitive categories (vitality products, intimate area treatments, lingerie, etc.), create tasteful presentations that avoid explicit imagery or language\r\n- Use euphemisms, metaphors and subtle visual cues instead of direct depictions\r\n- Focus on benefits like \"wellness,\" \"self-care,\" \"confidence\" rather than explicit functions\r\n- Maintain elegance and professionalism in all visual elements\r\n-  Avoid imagery that could trigger platform violations\r\n2. Cultural Representation\r\n- Match the ethnicity of people featured in the brochure with the location_target demographic\r\n- For Indonesia, use models with Indonesian features and appropriate cultural elements\r\n- For other countries, ensure appropriate ethnic representation aligned with the target market\r\n- Consider cultural sensitivities regarding clothing, gestures, and symbols\r\n3. Language Localization\r\n- Ensure all sample text for the banner/brochure (EXCEPT the CTA button) is in the appropriate language for the location_target\r\n- Adapt messaging style to cultural preferences (direct vs indirect communication)\r\n- Use culturally relevant idioms, references, and expressions\r\n- Avoid direct translations that might lose meaning or be inappropriate\r\n\r\n# Analysis Process:\r\n1. Identify strongest value propositions for target audience within cultural context\r\n2. Define visual elements that align with audience preferences and cultural norms\r\n3. Design compelling CTA specifically optimized to drive clicks on a SINGLE button (Either \"GET VOUCHER\" OR \"REDEEM VOUCHER\" exclusively (not both))\r\n4. Integrate trust-building visual elements\r\n5. Optimize for impulse purchasing behavior\r\n\r\n# Text Constraints:\r\n- Keep text in the image minimal and impactful\r\n- Focus on one main headline and one CTA\r\n- Avoid overwhelming visual space with words\r\n- Ensure language is appropriate for target market\r\n\r\nThe context should serve as a comprehensive creative brief that directs the image generator to create an effective, culturally appropriate marketing brochure that will resonate with the specific target market while avoiding platform violations for sensitive products.\r\n\r\nHere is the input data:\r\n<input>\r\n`;\r\n\r\nexport default {\r\n  async fetch(request: Request, env: any) {\r\n    if (request.method !== \"POST\") {\r\n      return new Response(\"Method Not Allowed\", { status: 405 });\r\n    }\r\n\r\n    const input = await request.json();\r\n\r\n    const messages: any[] = [\r\n      {\r\n        role: \"system\",\r\n        content: systemPrompt\r\n      },\r\n      {\r\n        role: \"user\",\r\n        content: JSON.stringify(input, null, 2)\r\n      },\r\n    ];\r\n    console.log(\"messages prompt context planner image generator\", JSON.stringify(messages))\r\n    const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${env.OPENAI_API_KEY}`,\r\n      },\r\n      body: JSON.stringify({\r\n        model: \"gpt-4o\",\r\n        messages,\r\n        temperature: 0.5,\r\n      }),\r\n    });\r\n\r\n    const result = await response.json();\r\n    // Ambil hanya konten JSON dari output OpenAI\r\n    let jsonContent = null;\r\n    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {\r\n      let content = result.choices[0].message.content;\r\n      // Hapus blok kode markdown jika ada\r\n      if (typeof content === 'string') {\r\n        content = content.trim();\r\n        if (content.startsWith('```json')) {\r\n          content = content.replace(/^```json\\s*/i, '').replace(/```\\s*$/i, '');\r\n        } else if (content.startsWith('```')) {\r\n          content = content.replace(/^```\\s*/i, '').replace(/```\\s*$/i, '');\r\n        }\r\n      }\r\n      try {\r\n        jsonContent = JSON.parse(content);\r\n      } catch (e) {\r\n        // Jika gagal parse, fallback ke raw content\r\n        jsonContent = content;\r\n      }\r\n    }\r\n    return new Response(JSON.stringify(jsonContent), {\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    });\r\n  },\r\n};\r\n", "const systemPrompt = `You are an expert prompt generator specializing in creating conversion-optimized digital brochures. Your primary task is to generate multiple distinct and highly optimized prompts (based on jumlah_brosur), utilizing advanced split-testing methodologies and performance optimization — with strategic decision logic focused on maximizing user action.\r\n# Input Context:\r\nExample input:\r\n{\r\n  \"product_description\": \"string\",\r\n  \"offer\": \"string\", \r\n  \"target_country\": \"indonesia\",\r\n  \"logo\": \"path_to_logo.jpg\",\r\n  \"product\": \"path_to_product.jpg\", \r\n  \"main_color\": \"#COLORCODE\",\r\n  \"jumlah_brosur\": integer,\r\n  \"project_key\": \"string\",\r\n  \"visual_style\": \"string\",\r\n  \"context_planner\": \"Design strategy from context planner\"\r\n}\r\n# Language Output Requirements:\r\n- Your prompt generation output MUST be in English\r\n- The CTA in the brochure must always remain in English as EITHER \"GET VOUCHER\" OR \"REDEEM VOUCHER\" (choose only one)\r\n- All other text elements in the brochure/advertisement should be in the target_country language\r\n- Include sample headline text and supporting copy in the appropriate target_country language\r\n- Provide English translations in parentheses for any non-English text examples\r\n\r\n# Split-Testing Strategy:\r\nFor each brochure (1 to jumlah_brosur), create variations with:\r\n- CTA Strategy: Either \"GET VOUCHER\" OR \"REDEEM VOUCHER\" exclusively (not both) - ALWAYS IN ENGLISH\r\n- Psychological triggers: Authority, scarcity, social proof, curiosity\r\n- Visual hierarchy: Test primary focus elements\r\n- Pricing presentation: Different offer positioning strategies\r\n- User journey: Direct vs. educational approaches\r\n- Product sensitivity adaptation: For sensitive products (vitality, intimate care, lingerie), test different levels of subtlety using metaphors and wellness-focused messaging\r\n- Cultural representation : Test variations of ethnic representation that accurately match target_country demographics\r\n- Language localization: Test different linguistic approaches appropriate to target_country (EXCEPT for CTA which remains in English)\r\n\r\n# 5-Layer Template (Per Brochure Variant):\r\n1. Subject Layer:\r\n- Variant A: Problem-Solution focus\r\n- Variant B: Benefit-first approach\r\n- Variant C: Fear-reduction messaging\r\n- For sensitive products: Focus on wellness, confidence, and self-care narratives rather than explicit functions\r\n- Cultural adaptation: Modify messaging to align with target_country values and taboos\r\n2. Style Layer:\r\n- Use visual_style + main_color with variations\r\n- Test different emotional tones\r\n- For sensitive products:Implement elegant, tasteful presentations with subtle visual cues\r\n- Cultural matching: Ensure aesthetic elements align with target_country preferences\r\n3. Layout Layer:\r\n- Variant A: Z-pattern for info flow\r\n- Variant B: F-pattern for reading\r\n- Variant C: Circular flow for engagement\r\n- For sensitive products: Strategic placement of product imagery to maintain sophistication\r\n- Cultural consideration: Adapt layout to target_country reading patterns and visual hierarchies\r\n4. Technical Layer:\r\n- 1024x1536 format\r\n- Strategic logo/product placement variations\r\n- Mobile-first considerations\r\n- For sensitive products: Soft lighting, abstract imagery, professional aesthetics to avoid platform violations\r\n- Cultural requirement: Feature models/people with ethnicity matching target_country\r\n5. Quality Layer:\r\n- Language requirement: All brochure text (EXCEPT CTA) must be in appropriate language for target_country\r\n- Target_Country text variations\r\n- Different formality levels\r\n- For sensitive products: Test euphemisms and indirect language that maintains marketing impact\r\n\r\n## Execution Strategy:\r\n\r\n1. Use context_planner as strategic foundation\r\n2. Generate jumlah_brosur unique variants\r\n3. Each variant must test different hypotheses while addressing these requirements\r\n- Primary motivation triggers relevant to target_country cultural values\r\n- Visual attention paths aligned with cultural reading patterns\r\n- Cognitive processing approaches that respect local taboos and preferences\r\n- Decision-making frameworks appropriate to the culture\r\n4. Product sensitivity analysis: \r\n- For standard products: Direct, benefit-focused presentation\r\n- For sensitive products (vitality, intimate areas, lingerie): Elegant, metaphorical presentation avoiding explicit imagery/language\r\n5. Cultural representation mandate:\r\n- For Indonesia: Feature authentic Indonesian models and cultural elements\r\n- For other markets: Implement correct ethnic representation matching target_country\r\n6. Language adaptation requirement:\r\n- All brochure text EXCEPT the CTA must use appropriate language for target_country\r\n- The CTA must always be in English: either \"GET VOUCHER\" or \"REDEEM VOUCHER\"\r\n- Incorporate culturally relevant expressions and communication styles in non-CTA text\r\n\r\n\r\n## Tips:\r\n- Context_planner provides audience insights - leverage for personalization\r\n-. Always integrate logo.jpg and product.jpg into prompts if available.\r\n-  Remove generic trust indicators (e.g., \"Trusted by...\")\r\n-  Premium feel with value emphasis\r\n- Circular product showcase center-stage\r\n- Test psychological principles: reciprocity, commitment, liking, authority\r\n- Vary information density across variants\r\n- Consider cultural nuances in target_country market\r\n- Optimize for mobile-first experience\r\n- When product category involves sensitive areas (vitality, intimate services, lingerie), focus on sophisticated presentation using metaphors and wellness narratives\r\n- Always ensure human imagery ethnicity matches target_country demographics\r\n- Respect religious and traditional values of target_country in all visual compositions\r\n- Keep CTA in English while adapting all other text to target country language\r\n\r\n\r\n##  Output Format:\r\nExample output:\r\n{\r\n  \"final_openai_prompts\": [\r\n    {\r\n      \"prompt\": \"[COMPREHENSIVE PROMPT FOR VARIANT 1]\",\r\n      \"image_size\": \"1024x1536\"\r\n    },\r\n    {\r\n      \"prompt\": \"[COMPREHENSIVE PROMPT FOR VARIANT 2]\",\r\n      \"image_size\": \"1024x1536\"\r\n    }\r\n    // ... continue for jumlah_brosur\r\n  ]\r\n}\r\n\r\n`;\r\n\r\nexport default {\r\n  async fetch(request: Request, env: any) {\r\n    if (request.method !== \"POST\") {\r\n      return new Response(\"Method Not Allowed\", { status: 405 });\r\n    }\r\n\r\n    const input = await request.json();\r\n\r\n    const messages: any[] = [\r\n      {\r\n        role: \"system\",\r\n        content: systemPrompt\r\n      },\r\n      {\r\n        role: \"user\",\r\n        content: JSON.stringify(input, null, 2)\r\n      },\r\n    ];\r\n    console.log(\"messages prompt generator\", JSON.stringify(messages))\r\n    const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${env.OPENAI_API_KEY}`,\r\n      },\r\n      body: JSON.stringify({\r\n        model: \"gpt-4o\",\r\n        messages,\r\n        temperature: 0.6,\r\n      }),\r\n    });\r\n\r\n    const result = await response.json();\r\n    // Ambil hanya konten JSON dari output OpenAI\r\n    let jsonContent = null;\r\n    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {\r\n      let content = result.choices[0].message.content;\r\n      // Hapus blok kode markdown jika ada\r\n      if (typeof content === 'string') {\r\n        content = content.trim();\r\n        if (content.startsWith('```json')) {\r\n          content = content.replace(/^```json\\s*/i, '').replace(/```\\s*$/i, '');\r\n        } else if (content.startsWith('```')) {\r\n          content = content.replace(/^```\\s*/i, '').replace(/```\\s*$/i, '');\r\n        }\r\n      }\r\n      try {\r\n        jsonContent = JSON.parse(content);\r\n      } catch (e) {\r\n        // Jika gagal parse, fallback ke raw content\r\n        jsonContent = content;\r\n      }\r\n    }\r\n    return new Response(JSON.stringify(jsonContent), {\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    });\r\n  },\r\n};\r\n", "import marketingAnalysisAgent from \"../agents/marketing_analysis\"\nimport productAudienceMatchingAgent from \"../agents/product_audience_matching\"\nimport contextPlannerImageAgent from \"../agents/context_planner_image_generator\"\nimport simplifiedBrochureAgent from \"../agents/simplified_digital_brochure_prompt_generator\"\n\nimport type { ExecutionContext } from '@cloudflare/workers-types'\n\n// Helper to call agent and get response JSON\nasync function callAgent(agent: any, request: Request, env: any, payload: any) {\n  const agentRequest = new Request(request, {\n    method: \"POST\",\n    body: JSON.stringify(payload),\n    headers: { \"Content-Type\": \"application/json\" },\n  })\n  const response = await agent.fetch(agentRequest, env)\n  return await response.json()\n}\n\n// Helper to extract key insights for context planner\nfunction extractContextPlannerInput(marketingResult: any, audienceMatchingResult: any) {\n  // 1. Product features, pricing, positioning\n  const product = {\n    features: marketingResult?.product_analysis?.key_features || [],\n    pricing: marketingResult?.product_analysis?.pricing || {},\n    positioning: marketingResult?.product_analysis?.market_position || \"\"\n  };\n\n  // 2. Target audience demographics and psychographics\n  const audience = {\n    demographics: marketingResult?.audience_segment?.demographics || {},\n    psychographics: marketingResult?.audience_segment?.psychographics || {},\n    segment_id: marketingResult?.audience_segment?.segment_id || \"\",\n    segment_name: marketingResult?.audience_segment?.segment_name || \"\"\n  };\n\n  // 3. Marketing strategy alignment\n  const marketing_strategy = marketingResult?.marketing_strategy || {};\n\n  // 4. Conversion optimization factors (from product_audience_matching)\n  let conversion_optimization = {};\n  if (audienceMatchingResult?.product_audience_matches && Array.isArray(audienceMatchingResult.product_audience_matches)) {\n    // Use the primary segment if available, else the first\n    const primaryId = audienceMatchingResult.segment_prioritization?.primary_segment_id;\n    const match = audienceMatchingResult.product_audience_matches.find((m: any) => m.segment_id === primaryId) || audienceMatchingResult.product_audience_matches[0];\n    if (match) {\n      conversion_optimization = {\n        conversion_strategy: match.conversion_strategy,\n        engagement_potential: match.engagement_potential,\n        competitive_position: match.competitive_position,\n        marketing_recommendations: match.marketing_recommendations,\n        overall_match_score: match.overall_match_score\n      };\n    }\n  }\n\n  return {\n    product,\n    audience,\n    marketing_strategy,\n    conversion_optimization\n  };\n}\n\nexport default {\n  async fetch(request: Request, env: any) {\n    const input = await request.json()\n\n    // 1. Marketing Analysis\n    const marketingResult = await callAgent(marketingAnalysisAgent, request, env, input)\n\n    console.log(\"marketingResult\", JSON.stringify(marketingResult));\n    // 2. Product Audience Matching\n    const audienceMatchingResult = await callAgent(productAudienceMatchingAgent, request, env, {\n      ...input,\n      marketing_data: marketingResult\n    })\n\n    console.log(\"audienceMatchingResult\", JSON.stringify(audienceMatchingResult));\n    // 3. Context Planner Image Generator\n    const contextPlannerInput = extractContextPlannerInput(marketingResult, audienceMatchingResult);\n    const contextPlannerResult = await callAgent(contextPlannerImageAgent, request, env, contextPlannerInput);\n\n    console.log(\"contextPlannerResult\", JSON.stringify(contextPlannerResult));\n    // 4. Simplified Digital Brochure Prompt Generator\n    const brochureResult = await callAgent(simplifiedBrochureAgent, request, env, {\n      ...input,\n      context_data: contextPlannerResult\n    })\n    console.log(\"brochureResult\", JSON.stringify(brochureResult));\n    // Return final result\n    return new Response(JSON.stringify({\n      marketing_analysis: marketingResult,\n      product_audience_matching: audienceMatchingResult,\n      context_planner_image: contextPlannerResult,\n      brochure_prompt: brochureResult\n    }), {\n      headers: { \"Content-Type\": \"application/json\" }\n    })\n  }\n}\n", "\nimport contextPlannerImageAgent from \"./agents/context_planner_image_generator\"\nimport simplifiedBrochureAgent from \"./agents/simplified_digital_brochure_prompt_generator\"\nimport productAudienceMatchingAgent from \"./agents/product_audience_matching\"\nimport marketingAnalysisAgent from \"./agents/marketing_analysis\"\nimport taskWorkflow from \"./workflow/task_workflow\"\nimport type { ExecutionContext } from '@cloudflare/workers-types'\n\nexport default {\n  async fetch(request: Request, env: any) {\n    const url = new URL(request.url);\n\n    if (url.pathname === \"/agent/marketing-analysis\") {\n      return marketingAnalysisAgent.fetch(request, env);\n    }\n\n    if (url.pathname === \"/agent/product-audience-matching\") {\n      return productAudienceMatchingAgent.fetch(request, env);\n    }\n\n    if (url.pathname === \"/workflow/marketing-brochure\") {\n      return taskWorkflow.fetch(request, env);\n    }\n\n    return new Response(\"Not Found\", { status: 404 });\n  },\n};\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\backup-HD-lama\\\\AI\\\\agent-cloudflare\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\npm-cache\\\\_npx\\\\32026684e21afda6\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\n\n\t\t\t\texport * from \"E:\\\\backup-HD-lama\\\\AI\\\\agent-cloudflare\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\backup-HD-lama\\\\AI\\\\agent-cloudflare\\\\.wrangler\\\\tmp\\\\bundle-aSfhnm\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\npm-cache\\\\_npx\\\\32026684e21afda6\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\backup-HD-lama\\\\AI\\\\agent-cloudflare\\\\.wrangler\\\\tmp\\\\bundle-aSfhnm\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\backup-HD-lama\\\\AI\\\\agent-cloudflare\\\\.wrangler\\\\tmp\\\\bundle-aSfhnm\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2FrB,IAAO,oCAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,QAAI,QAAQ,WAAW,QAAQ;AAC7B,aAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC3D;AAEA,UAAM,QAAQ,MAAM,QAAQ,KAAK;AAEjC,UAAM,WAAkB;AAAA,MACtB;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,MAAM,MAAM;AACd,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,UACP,EAAE,MAAM,QAAQ,MAAM,sCAAsC;AAAA,UAC5D,EAAE,MAAM,aAAa,WAAW,EAAE,KAAK,MAAM,KAAK,EAAE;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,MAAM,SAAS;AACjB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,UACP,EAAE,MAAM,QAAQ,MAAM,yCAAyC;AAAA,UAC/D,EAAE,MAAM,aAAa,WAAW,EAAE,KAAK,MAAM,QAAQ,EAAE;AAAA,QACzD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,cAAc,KAAK,UAAU,OAAO,MAAM,CAAC;AAEjD,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAED,YAAQ,IAAI,6CAA6C,KAAK,UAAU,QAAQ,CAAC;AACjF,UAAM,WAAW,MAAM,MAAM,8CAA8C;AAAA,MACzE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,eAAe,UAAU,IAAI,cAAc;AAAA,MAC7C;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,OAAO;AAAA,QACP;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAED,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,QAAI,cAAc;AAClB,QAAI,UAAU,OAAO,WAAW,OAAO,QAAQ,CAAC,KAAK,OAAO,QAAQ,CAAC,EAAE,WAAW,OAAO,QAAQ,CAAC,EAAE,QAAQ,SAAS;AACnH,UAAI,UAAU,OAAO,QAAQ,CAAC,EAAE,QAAQ;AAExC,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,QAAQ,KAAK;AACvB,YAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,oBAAU,QAAQ,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QACtE,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,oBAAU,QAAQ,QAAQ,YAAY,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QAClE;AAAA,MACF;AACA,UAAI;AACF,sBAAc,KAAK,MAAM,OAAO;AAAA,MAClC,SAAS,GAAG;AAEV,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,WAAO,IAAI,SAAS,KAAK,UAAU,WAAW,GAAG;AAAA,MAC/C,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;AC5KA,IAAMA,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6KrB,IAAO,6BAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,YAAQ,IAAI,2BAA2B;AAEvC,QAAI,QAAQ,WAAW,QAAQ;AAC7B,aAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC3D;AAEA,QAAI;AACF,YAAM,QAAQ,MAAM,QAAQ,KAAK;AACjC,cAAQ,IAAI,mBAAmB,KAAK;AAGpC,YAAM,WAAkB;AAAA,QACtB;AAAA,UACE,MAAM;AAAA,UACN,SAASA;AAAA,QACX;AAAA,MACF;AAGA,UAAI,MAAM,MAAM;AACd,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,YACP,EAAE,MAAM,QAAQ,MAAM,sCAAsC;AAAA,YAC5D,EAAE,MAAM,aAAa,WAAW,EAAE,KAAK,MAAM,KAAK,EAAE;AAAA,UACtD;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,MAAM,SAAS;AACjB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,YACP,EAAE,MAAM,QAAQ,MAAM,yCAAyC;AAAA,YAC/D,EAAE,MAAM,aAAa,WAAW,EAAE,KAAK,MAAM,QAAQ,EAAE;AAAA,UACzD;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,cAAc,KAAK,UAAU,OAAO,MAAM,CAAC;AAGjD,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAED,cAAQ,IAAI,sCAAsC,KAAK,UAAU,QAAQ,CAAC;AAE1E,YAAM,WAAW,MAAM,MAAM,8CAA8C;AAAA,QACzE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,eAAe,UAAU,IAAI,cAAc;AAAA,QAC7C;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,OAAO;AAAA,UACP;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAED,YAAM,SAAS,MAAM,SAAS,KAAK;AAInC,UAAI,cAAc;AAClB,UAAI,QAAQ,UAAU,CAAC,GAAG,SAAS,SAAS;AAC1C,YAAI,UAAU,OAAO,QAAQ,CAAC,EAAE,QAAQ,QAAQ,KAAK;AAGrD,YAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,oBAAU,QAAQ,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QACtE,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,oBAAU,QAAQ,QAAQ,YAAY,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QAClE;AAEA,YAAI;AACF,wBAAc,KAAK,MAAM,OAAO;AAAA,QAClC,SAAS,GAAG;AACV,kBAAQ,KAAK,uCAAuC;AACpD,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,aAAO,IAAI,SAAS,KAAK,UAAU,WAAW,GAAG;AAAA,QAC/C,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IAEH,SAAS,GAAQ;AACf,cAAQ,MAAM,6BAA6B,CAAC;AAC5C,aAAO,IAAI,SAAS,4BAA4B,EAAE,SAAS,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC5E;AAAA,EACF;AACF;;;AC/QA,IAAMC,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuErB,IAAO,0CAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,QAAI,QAAQ,WAAW,QAAQ;AAC7B,aAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC3D;AAEA,UAAM,QAAQ,MAAM,QAAQ,KAAK;AAEjC,UAAM,WAAkB;AAAA,MACtB;AAAA,QACE,MAAM;AAAA,QACN,SAASA;AAAA,MACX;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS,KAAK,UAAU,OAAO,MAAM,CAAC;AAAA,MACxC;AAAA,IACF;AACA,YAAQ,IAAI,mDAAmD,KAAK,UAAU,QAAQ,CAAC;AACvF,UAAM,WAAW,MAAM,MAAM,8CAA8C;AAAA,MACzE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,eAAe,UAAU,IAAI,cAAc;AAAA,MAC7C;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,OAAO;AAAA,QACP;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAED,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,QAAI,cAAc;AAClB,QAAI,UAAU,OAAO,WAAW,OAAO,QAAQ,CAAC,KAAK,OAAO,QAAQ,CAAC,EAAE,WAAW,OAAO,QAAQ,CAAC,EAAE,QAAQ,SAAS;AACnH,UAAI,UAAU,OAAO,QAAQ,CAAC,EAAE,QAAQ;AAExC,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,QAAQ,KAAK;AACvB,YAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,oBAAU,QAAQ,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QACtE,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,oBAAU,QAAQ,QAAQ,YAAY,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QAClE;AAAA,MACF;AACA,UAAI;AACF,sBAAc,KAAK,MAAM,OAAO;AAAA,MAClC,SAAS,GAAG;AAEV,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,WAAO,IAAI,SAAS,KAAK,UAAU,WAAW,GAAG;AAAA,MAC/C,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;AChIA,IAAMC,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsHrB,IAAO,uDAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,QAAI,QAAQ,WAAW,QAAQ;AAC7B,aAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC3D;AAEA,UAAM,QAAQ,MAAM,QAAQ,KAAK;AAEjC,UAAM,WAAkB;AAAA,MACtB;AAAA,QACE,MAAM;AAAA,QACN,SAASA;AAAA,MACX;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS,KAAK,UAAU,OAAO,MAAM,CAAC;AAAA,MACxC;AAAA,IACF;AACA,YAAQ,IAAI,6BAA6B,KAAK,UAAU,QAAQ,CAAC;AACjE,UAAM,WAAW,MAAM,MAAM,8CAA8C;AAAA,MACzE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,eAAe,UAAU,IAAI,cAAc;AAAA,MAC7C;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,OAAO;AAAA,QACP;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAED,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,QAAI,cAAc;AAClB,QAAI,UAAU,OAAO,WAAW,OAAO,QAAQ,CAAC,KAAK,OAAO,QAAQ,CAAC,EAAE,WAAW,OAAO,QAAQ,CAAC,EAAE,QAAQ,SAAS;AACnH,UAAI,UAAU,OAAO,QAAQ,CAAC,EAAE,QAAQ;AAExC,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,QAAQ,KAAK;AACvB,YAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,oBAAU,QAAQ,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QACtE,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,oBAAU,QAAQ,QAAQ,YAAY,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,QAClE;AAAA,MACF;AACA,UAAI;AACF,sBAAc,KAAK,MAAM,OAAO;AAAA,MAClC,SAAS,GAAG;AAEV,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,WAAO,IAAI,SAAS,KAAK,UAAU,WAAW,GAAG;AAAA,MAC/C,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;ACvKA,eAAe,UAAU,OAAY,SAAkB,KAAU,SAAc;AAC7E,QAAM,eAAe,IAAI,QAAQ,SAAS;AAAA,IACxC,QAAQ;AAAA,IACR,MAAM,KAAK,UAAU,OAAO;AAAA,IAC5B,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,EAChD,CAAC;AACD,QAAM,WAAW,MAAM,MAAM,MAAM,cAAc,GAAG;AACpD,SAAO,MAAM,SAAS,KAAK;AAC7B;AARe;AAWf,SAAS,2BAA2B,iBAAsB,wBAA6B;AAErF,QAAM,UAAU;AAAA,IACd,UAAU,iBAAiB,kBAAkB,gBAAgB,CAAC;AAAA,IAC9D,SAAS,iBAAiB,kBAAkB,WAAW,CAAC;AAAA,IACxD,aAAa,iBAAiB,kBAAkB,mBAAmB;AAAA,EACrE;AAGA,QAAM,WAAW;AAAA,IACf,cAAc,iBAAiB,kBAAkB,gBAAgB,CAAC;AAAA,IAClE,gBAAgB,iBAAiB,kBAAkB,kBAAkB,CAAC;AAAA,IACtE,YAAY,iBAAiB,kBAAkB,cAAc;AAAA,IAC7D,cAAc,iBAAiB,kBAAkB,gBAAgB;AAAA,EACnE;AAGA,QAAM,qBAAqB,iBAAiB,sBAAsB,CAAC;AAGnE,MAAI,0BAA0B,CAAC;AAC/B,MAAI,wBAAwB,4BAA4B,MAAM,QAAQ,uBAAuB,wBAAwB,GAAG;AAEtH,UAAM,YAAY,uBAAuB,wBAAwB;AACjE,UAAM,QAAQ,uBAAuB,yBAAyB,KAAK,CAAC,MAAW,EAAE,eAAe,SAAS,KAAK,uBAAuB,yBAAyB,CAAC;AAC/J,QAAI,OAAO;AACT,gCAA0B;AAAA,QACxB,qBAAqB,MAAM;AAAA,QAC3B,sBAAsB,MAAM;AAAA,QAC5B,sBAAsB,MAAM;AAAA,QAC5B,2BAA2B,MAAM;AAAA,QACjC,qBAAqB,MAAM;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AA1CS;AA4CT,IAAO,wBAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,UAAM,QAAQ,MAAM,QAAQ,KAAK;AAGjC,UAAM,kBAAkB,MAAM,UAAU,4BAAwB,SAAS,KAAK,KAAK;AAEnF,YAAQ,IAAI,mBAAmB,KAAK,UAAU,eAAe,CAAC;AAE9D,UAAM,yBAAyB,MAAM,UAAU,mCAA8B,SAAS,KAAK;AAAA,MACzF,GAAG;AAAA,MACH,gBAAgB;AAAA,IAClB,CAAC;AAED,YAAQ,IAAI,0BAA0B,KAAK,UAAU,sBAAsB,CAAC;AAE5E,UAAM,sBAAsB,2BAA2B,iBAAiB,sBAAsB;AAC9F,UAAM,uBAAuB,MAAM,UAAU,yCAA0B,SAAS,KAAK,mBAAmB;AAExG,YAAQ,IAAI,wBAAwB,KAAK,UAAU,oBAAoB,CAAC;AAExE,UAAM,iBAAiB,MAAM,UAAU,sDAAyB,SAAS,KAAK;AAAA,MAC5E,GAAG;AAAA,MACH,cAAc;AAAA,IAChB,CAAC;AACD,YAAQ,IAAI,kBAAkB,KAAK,UAAU,cAAc,CAAC;AAE5D,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,oBAAoB;AAAA,MACpB,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,iBAAiB;AAAA,IACnB,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;AC3FA,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU;AACtC,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAE/B,QAAI,IAAI,aAAa,6BAA6B;AAChD,aAAO,2BAAuB,MAAM,SAAS,GAAG;AAAA,IAClD;AAEA,QAAI,IAAI,aAAa,oCAAoC;AACvD,aAAO,kCAA6B,MAAM,SAAS,GAAG;AAAA,IACxD;AAEA,QAAI,IAAI,aAAa,gCAAgC;AACnD,aAAO,sBAAa,MAAM,SAAS,GAAG;AAAA,IACxC;AAEA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;;;ACxBA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACZJ,IAAM,mCAAmC;AAAA,EAE9B;AAClB;AACA,IAAO,sCAAQ;;;ACenB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["systemPrompt", "systemPrompt", "systemPrompt"]}