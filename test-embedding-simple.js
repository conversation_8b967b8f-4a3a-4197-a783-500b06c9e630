// Test script sederhana untuk fungsi embedding
// Jalankan dengan: node test-embedding-simple.js

const crypto = require('crypto');

// Mock OpenAI untuk testing tanpa API key
class MockOpenAI {
  constructor(config) {
    this.apiKey = config.apiKey;
  }
  
  get embeddings() {
    return {
      create: async (params) => {
        console.log(`🔄 Mock: Generating embedding for text: "${params.input.substring(0, 50)}..."`);
        
        // Generate mock embedding dengan 1536 dimensi (sesuai text-embedding-ada-002)
        const embedding = Array.from({ length: 1536 }, () => Math.random() * 2 - 1);
        
        return {
          data: [{
            embedding: embedding,
            index: 0,
            object: 'embedding'
          }],
          model: params.model,
          object: 'list',
          usage: {
            prompt_tokens: params.input.split(' ').length,
            total_tokens: params.input.split(' ').length
          }
        };
      }
    };
  }
}

// Mock environment
const mockEnv = {
  OPENAI_API_KEY: 'mock-api-key',
  VECTOR_KV: {
    data: new Map(),
    async put(key, value) {
      this.data.set(key, value);
      console.log(`✅ KV Store: Saved ${key}`);
    },
    async get(key) {
      return this.data.get(key) || null;
    },
    async list(options = {}) {
      const keys = Array.from(this.data.keys())
        .filter(key => !options.prefix || key.startsWith(options.prefix))
        .map(name => ({ name }));
      return { keys };
    }
  },
  VECTORIZE: {
    vectors: [],
    async upsert(vectors) {
      this.vectors.push(...vectors);
      console.log(`✅ Vectorize: Upserted ${vectors.length} vectors`);
      return { count: vectors.length };
    },
    async query(options) {
      const matches = this.vectors
        .filter(vector => {
          if (!options.filter) return true;
          return Object.entries(options.filter).every(([key, value]) => 
            vector.metadata && vector.metadata[key] === value
          );
        })
        .map(vector => ({
          id: vector.id,
          score: Math.random() * 0.3 + 0.7, // Score antara 0.7-1.0
          values: vector.values,
          metadata: vector.metadata
        }));
      
      matches.sort((a, b) => b.score - a.score);
      return { 
        matches: matches.slice(0, options.topK || 5)
      };
    }
  }
};

// Implementasi fungsi getEmbedding untuk testing
async function getEmbedding(text, env) {
  const openai = new MockOpenAI({ apiKey: env.OPENAI_API_KEY });
  const embeddingRes = await openai.embeddings.create({
    model: "text-embedding-ada-002",
    input: text
  });
  return embeddingRes.data[0].embedding;
}

// Implementasi fungsi lainnya
async function storeVectorDoc(doc, env) {
  await env.VECTOR_KV.put(`vector:${doc.id}`, JSON.stringify(doc));
}

async function getAllVectorDocs(env) {
  const list = await env.VECTOR_KV.list({ prefix: "vector:" });
  const docs = [];
  for (const key of list.keys) {
    const val = await env.VECTOR_KV.get(key.name);
    if (val) docs.push(JSON.parse(val));
  }
  return docs;
}

function cosineSimilarity(a, b) {
  let dot = 0, normA = 0, normB = 0;
  for (let i = 0; i < a.length; i++) {
    dot += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  return dot / (Math.sqrt(normA) * Math.sqrt(normB));
}

async function retrieveRelevantDocs(query, env, topK = 3) {
  const queryEmbedding = await getEmbedding(query, env);
  const docs = await getAllVectorDocs(env);
  
  const scored = docs.map(doc => ({
    ...doc,
    score: cosineSimilarity(queryEmbedding, doc.embedding)
  }));
  
  scored.sort((a, b) => b.score - a.score);
  return scored.slice(0, topK);
}

// Test functions
async function testGetEmbedding() {
  console.log('\n🧪 Test 1: getEmbedding function');
  console.log('================================');
  
  const testText = "Machine learning adalah bagian dari artificial intelligence";
  console.log(`Input: "${testText}"`);
  
  const embedding = await getEmbedding(testText, mockEnv);
  
  console.log(`✅ Embedding berhasil dibuat!`);
  console.log(`   - Tipe: ${typeof embedding}`);
  console.log(`   - Array: ${Array.isArray(embedding)}`);
  console.log(`   - Panjang: ${embedding.length}`);
  console.log(`   - Sample values: [${embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
  
  // Validasi
  if (!Array.isArray(embedding)) {
    throw new Error('Embedding harus berupa array');
  }
  
  if (embedding.length !== 1536) {
    console.log(`⚠️  Warning: Expected 1536 dimensions, got ${embedding.length}`);
  }
  
  return embedding;
}

async function testStoreRetrieve() {
  console.log('\n🧪 Test 2: Store & Retrieve functions');
  console.log('====================================');
  
  // Data test
  const testDocs = [
    {
      id: 'doc1',
      content: 'Machine learning adalah subset dari artificial intelligence',
      meta: { kategori: 'AI', sumber: 'test' }
    },
    {
      id: 'doc2',
      content: 'Deep learning menggunakan neural network dengan banyak layer',
      meta: { kategori: 'AI', sumber: 'test' }
    },
    {
      id: 'doc3',
      content: 'Memasak nasi memerlukan air dan beras yang berkualitas',
      meta: { kategori: 'masakan', sumber: 'test' }
    }
  ];
  
  // Store documents
  console.log('Menyimpan dokumen...');
  for (const doc of testDocs) {
    const embedding = await getEmbedding(doc.content, mockEnv);
    const vectorDoc = { ...doc, embedding };
    await storeVectorDoc(vectorDoc, mockEnv);
  }
  
  // Retrieve all
  const allDocs = await getAllVectorDocs(mockEnv);
  console.log(`✅ Berhasil retrieve ${allDocs.length} dokumen`);
  
  // Test similarity search
  console.log('\nTesting similarity search...');
  const query = 'Apa itu kecerdasan buatan?';
  console.log(`Query: "${query}"`);
  
  const relevantDocs = await retrieveRelevantDocs(query, mockEnv, 2);
  console.log(`✅ Ditemukan ${relevantDocs.length} dokumen relevan:`);
  
  relevantDocs.forEach((doc, index) => {
    console.log(`   ${index + 1}. [Score: ${doc.score?.toFixed(4)}] ${doc.content}`);
  });
}

async function testEndpoints() {
  console.log('\n🧪 Test 3: Embedding Endpoints');
  console.log('==============================');
  
  // Test /ingest endpoint
  console.log('Testing /ingest endpoint...');
  const ingestData = {
    text: 'Ini adalah konten test untuk ingestion',
    session_id: 'test-session-123',
    agent: 'test-agent'
  };
  
  const embedding = await getEmbedding(ingestData.text, mockEnv);
  const vectorId = crypto.randomUUID();
  
  await mockEnv.VECTORIZE.upsert([{
    id: vectorId,
    values: embedding,
    metadata: {
      session_id: ingestData.session_id,
      agent: ingestData.agent
    }
  }]);
  
  console.log(`✅ Ingest berhasil! Vector ID: ${vectorId}`);
  
  // Test /context endpoint
  console.log('\nTesting /context endpoint...');
  const contextQuery = 'konten test';
  const queryEmbedding = await getEmbedding(contextQuery, mockEnv);
  
  const results = await mockEnv.VECTORIZE.query({
    topK: 5,
    vector: queryEmbedding,
    filter: { session_id: 'test-session-123' },
    returnMetadata: true
  });
  
  console.log(`✅ Context search berhasil! Ditemukan ${results.matches.length} matches`);
  results.matches.forEach((match, index) => {
    console.log(`   ${index + 1}. Score: ${match.score.toFixed(4)}, Agent: ${match.metadata?.agent}`);
  });
}

// Main test runner
async function runTests() {
  console.log('🚀 Memulai Test Embedding...');
  console.log('============================');
  
  try {
    await testGetEmbedding();
    await testStoreRetrieve();
    await testEndpoints();
    
    console.log('\n🎉 Semua test berhasil!');
    console.log('=======================');
    
  } catch (error) {
    console.error('\n💥 Test gagal:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Jalankan test
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testGetEmbedding,
  testStoreRetrieve,
  testEndpoints,
  getEmbedding,
  mockEnv
};
