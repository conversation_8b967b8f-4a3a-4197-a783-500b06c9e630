"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionJobOut$ = exports.CompletionJobOut$outboundSchema = exports.CompletionJobOut$inboundSchema = exports.Repositories$ = exports.Repositories$outboundSchema = exports.Repositories$inboundSchema = exports.JobType$ = exports.JobType$outboundSchema = exports.JobType$inboundSchema = exports.Integrations$ = exports.Integrations$outboundSchema = exports.Integrations$inboundSchema = exports.ObjectT$ = exports.ObjectT$outboundSchema = exports.ObjectT$inboundSchema = exports.Status$ = exports.Status$outboundSchema = exports.Status$inboundSchema = exports.JobType = exports.ObjectT = exports.Status = void 0;
exports.integrationsToJSON = integrationsToJSON;
exports.integrationsFromJSON = integrationsFromJSON;
exports.repositoriesToJSON = repositoriesToJSON;
exports.repositoriesFromJSON = repositoriesFromJSON;
exports.completionJobOutToJSON = completionJobOutToJSON;
exports.completionJobOutFromJSON = completionJobOutFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const completiontrainingparameters_js_1 = require("./completiontrainingparameters.js");
const githubrepositoryout_js_1 = require("./githubrepositoryout.js");
const jobmetadataout_js_1 = require("./jobmetadataout.js");
const wandbintegrationout_js_1 = require("./wandbintegrationout.js");
/**
 * The current status of the fine-tuning job.
 */
exports.Status = {
    Queued: "QUEUED",
    Started: "STARTED",
    Validating: "VALIDATING",
    Validated: "VALIDATED",
    Running: "RUNNING",
    FailedValidation: "FAILED_VALIDATION",
    Failed: "FAILED",
    Success: "SUCCESS",
    Cancelled: "CANCELLED",
    CancellationRequested: "CANCELLATION_REQUESTED",
};
/**
 * The object type of the fine-tuning job.
 */
exports.ObjectT = {
    Job: "job",
};
/**
 * The type of job (`FT` for fine-tuning).
 */
exports.JobType = {
    Completion: "completion",
};
/** @internal */
exports.Status$inboundSchema = z
    .nativeEnum(exports.Status);
/** @internal */
exports.Status$outboundSchema = exports.Status$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Status$;
(function (Status$) {
    /** @deprecated use `Status$inboundSchema` instead. */
    Status$.inboundSchema = exports.Status$inboundSchema;
    /** @deprecated use `Status$outboundSchema` instead. */
    Status$.outboundSchema = exports.Status$outboundSchema;
})(Status$ || (exports.Status$ = Status$ = {}));
/** @internal */
exports.ObjectT$inboundSchema = z
    .nativeEnum(exports.ObjectT);
/** @internal */
exports.ObjectT$outboundSchema = exports.ObjectT$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ObjectT$;
(function (ObjectT$) {
    /** @deprecated use `ObjectT$inboundSchema` instead. */
    ObjectT$.inboundSchema = exports.ObjectT$inboundSchema;
    /** @deprecated use `ObjectT$outboundSchema` instead. */
    ObjectT$.outboundSchema = exports.ObjectT$outboundSchema;
})(ObjectT$ || (exports.ObjectT$ = ObjectT$ = {}));
/** @internal */
exports.Integrations$inboundSchema = wandbintegrationout_js_1.WandbIntegrationOut$inboundSchema;
/** @internal */
exports.Integrations$outboundSchema = wandbintegrationout_js_1.WandbIntegrationOut$outboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Integrations$;
(function (Integrations$) {
    /** @deprecated use `Integrations$inboundSchema` instead. */
    Integrations$.inboundSchema = exports.Integrations$inboundSchema;
    /** @deprecated use `Integrations$outboundSchema` instead. */
    Integrations$.outboundSchema = exports.Integrations$outboundSchema;
})(Integrations$ || (exports.Integrations$ = Integrations$ = {}));
function integrationsToJSON(integrations) {
    return JSON.stringify(exports.Integrations$outboundSchema.parse(integrations));
}
function integrationsFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Integrations$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Integrations' from JSON`);
}
/** @internal */
exports.JobType$inboundSchema = z
    .nativeEnum(exports.JobType);
/** @internal */
exports.JobType$outboundSchema = exports.JobType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var JobType$;
(function (JobType$) {
    /** @deprecated use `JobType$inboundSchema` instead. */
    JobType$.inboundSchema = exports.JobType$inboundSchema;
    /** @deprecated use `JobType$outboundSchema` instead. */
    JobType$.outboundSchema = exports.JobType$outboundSchema;
})(JobType$ || (exports.JobType$ = JobType$ = {}));
/** @internal */
exports.Repositories$inboundSchema = githubrepositoryout_js_1.GithubRepositoryOut$inboundSchema;
/** @internal */
exports.Repositories$outboundSchema = githubrepositoryout_js_1.GithubRepositoryOut$outboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Repositories$;
(function (Repositories$) {
    /** @deprecated use `Repositories$inboundSchema` instead. */
    Repositories$.inboundSchema = exports.Repositories$inboundSchema;
    /** @deprecated use `Repositories$outboundSchema` instead. */
    Repositories$.outboundSchema = exports.Repositories$outboundSchema;
})(Repositories$ || (exports.Repositories$ = Repositories$ = {}));
function repositoriesToJSON(repositories) {
    return JSON.stringify(exports.Repositories$outboundSchema.parse(repositories));
}
function repositoriesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Repositories$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Repositories' from JSON`);
}
/** @internal */
exports.CompletionJobOut$inboundSchema = z.object({
    id: z.string(),
    auto_start: z.boolean(),
    model: z.string(),
    status: exports.Status$inboundSchema,
    created_at: z.number().int(),
    modified_at: z.number().int(),
    training_files: z.array(z.string()),
    validation_files: z.nullable(z.array(z.string())).optional(),
    object: exports.ObjectT$inboundSchema.default("job"),
    fine_tuned_model: z.nullable(z.string()).optional(),
    suffix: z.nullable(z.string()).optional(),
    integrations: z.nullable(z.array(wandbintegrationout_js_1.WandbIntegrationOut$inboundSchema))
        .optional(),
    trained_tokens: z.nullable(z.number().int()).optional(),
    metadata: z.nullable(jobmetadataout_js_1.JobMetadataOut$inboundSchema).optional(),
    job_type: exports.JobType$inboundSchema.default("completion"),
    hyperparameters: completiontrainingparameters_js_1.CompletionTrainingParameters$inboundSchema,
    repositories: z.array(githubrepositoryout_js_1.GithubRepositoryOut$inboundSchema).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "auto_start": "autoStart",
        "created_at": "createdAt",
        "modified_at": "modifiedAt",
        "training_files": "trainingFiles",
        "validation_files": "validationFiles",
        "fine_tuned_model": "fineTunedModel",
        "trained_tokens": "trainedTokens",
        "job_type": "jobType",
    });
});
/** @internal */
exports.CompletionJobOut$outboundSchema = z.object({
    id: z.string(),
    autoStart: z.boolean(),
    model: z.string(),
    status: exports.Status$outboundSchema,
    createdAt: z.number().int(),
    modifiedAt: z.number().int(),
    trainingFiles: z.array(z.string()),
    validationFiles: z.nullable(z.array(z.string())).optional(),
    object: exports.ObjectT$outboundSchema.default("job"),
    fineTunedModel: z.nullable(z.string()).optional(),
    suffix: z.nullable(z.string()).optional(),
    integrations: z.nullable(z.array(wandbintegrationout_js_1.WandbIntegrationOut$outboundSchema))
        .optional(),
    trainedTokens: z.nullable(z.number().int()).optional(),
    metadata: z.nullable(jobmetadataout_js_1.JobMetadataOut$outboundSchema).optional(),
    jobType: exports.JobType$outboundSchema.default("completion"),
    hyperparameters: completiontrainingparameters_js_1.CompletionTrainingParameters$outboundSchema,
    repositories: z.array(githubrepositoryout_js_1.GithubRepositoryOut$outboundSchema).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        autoStart: "auto_start",
        createdAt: "created_at",
        modifiedAt: "modified_at",
        trainingFiles: "training_files",
        validationFiles: "validation_files",
        fineTunedModel: "fine_tuned_model",
        trainedTokens: "trained_tokens",
        jobType: "job_type",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionJobOut$;
(function (CompletionJobOut$) {
    /** @deprecated use `CompletionJobOut$inboundSchema` instead. */
    CompletionJobOut$.inboundSchema = exports.CompletionJobOut$inboundSchema;
    /** @deprecated use `CompletionJobOut$outboundSchema` instead. */
    CompletionJobOut$.outboundSchema = exports.CompletionJobOut$outboundSchema;
})(CompletionJobOut$ || (exports.CompletionJobOut$ = CompletionJobOut$ = {}));
function completionJobOutToJSON(completionJobOut) {
    return JSON.stringify(exports.CompletionJobOut$outboundSchema.parse(completionJobOut));
}
function completionJobOutFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionJobOut$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionJobOut' from JSON`);
}
//# sourceMappingURL=completionjobout.js.map