// Script untuk mendiagnosis masalah /context endpoint
const BASE_URL = 'https://ai.klikbuy.co.id';

async function makeRequest(endpoint, method = 'GET', body = null) {
  const url = `${BASE_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const responseText = await response.text();
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    };
    
  } catch (error) {
    console.error(`❌ Request failed: ${error.message}`);
    throw error;
  }
}

async function diagnoseContextEndpoint() {
  console.log('🔍 Diagnosing /context Endpoint Issues');
  console.log('=====================================');
  
  // Step 1: Check available system prompts
  console.log('\n📋 Step 1: Checking available system prompts...');
  
  try {
    const listResult = await makeRequest('/list-system-prompts');
    
    if (listResult.ok) {
      console.log(`✅ Found ${listResult.data.keys.length} system prompts:`);
      listResult.data.keys.slice(0, 5).forEach((key, index) => {
        console.log(`   ${index + 1}. ${key}`);
      });
      
      if (listResult.data.keys.length > 5) {
        console.log(`   ... and ${listResult.data.keys.length - 5} more`);
      }
      
      // Step 2: Test getting a specific system prompt
      console.log('\n📝 Step 2: Testing system prompt retrieval...');
      
      const testPrompt = listResult.data.keys[0]; // Use first available prompt
      console.log(`Testing with prompt: ${testPrompt}`);
      
      const getResult = await makeRequest(`/get-system-prompt?agent=${testPrompt}`);
      
      if (getResult.ok) {
        console.log(`✅ System prompt retrieved successfully`);
        console.log(`   Length: ${getResult.data.prompt.length} characters`);
        console.log(`   Preview: "${getResult.data.prompt.substring(0, 100)}..."`);
        
        // Step 3: Test context endpoint with this prompt
        console.log('\n🔍 Step 3: Testing /context endpoint...');
        
        const contextData = {
          system_prompt: testPrompt,
          session_id: 'diagnostic-session-123'
        };
        
        console.log(`Request body: ${JSON.stringify(contextData, null, 2)}`);
        
        const contextResult = await makeRequest('/context', 'POST', contextData);
        
        if (contextResult.ok) {
          console.log(`✅ Context endpoint working!`);
          console.log(`   Context length: ${contextResult.data.context?.length || 0}`);
        } else {
          console.log(`❌ Context endpoint failed: ${contextResult.status}`);
          
          // Analyze the error
          if (typeof contextResult.data === 'string') {
            if (contextResult.data.includes('Worker threw exception')) {
              console.log(`💡 Analysis: Worker exception occurred`);
              
              // Extract error details from HTML if possible
              const titleMatch = contextResult.data.match(/<title>(.*?)<\/title>/);
              if (titleMatch) {
                console.log(`   Error title: ${titleMatch[1]}`);
              }
              
              console.log(`\n🔍 Possible causes:`);
              console.log(`   1. OpenAI API key issue`);
              console.log(`   2. Embedding generation failed`);
              console.log(`   3. Vectorize query error`);
              console.log(`   4. Memory/timeout issue`);
              
            } else if (contextResult.data.includes('Embedding failed')) {
              console.log(`💡 Analysis: Embedding generation failed`);
              console.log(`   Check OpenAI API key and quota`);
            }
          } else {
            console.log(`   Error data: ${JSON.stringify(contextResult.data, null, 2)}`);
          }
        }
        
      } else {
        console.log(`❌ Failed to get system prompt: ${getResult.status}`);
      }
      
    } else {
      console.log(`❌ Failed to list system prompts: ${listResult.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Diagnosis failed: ${error.message}`);
  }
}

async function testEmbeddingFlow() {
  console.log('\n🧪 Testing Embedding Flow Components');
  console.log('====================================');
  
  // Test 1: Check if we can ingest data (this tests getEmbedding function)
  console.log('\n📥 Test 1: Testing embedding generation via /ingest...');
  
  const testData = {
    text: "Short test for embedding generation",
    session_id: "diagnostic-session-123",
    agent: "diagnostic-agent"
  };
  
  try {
    const ingestResult = await makeRequest('/ingest', 'POST', testData);
    
    if (ingestResult.ok) {
      console.log(`✅ Embedding generation working (via /ingest)`);
      console.log(`   Vector ID: ${ingestResult.data.id}`);
      
      // Test 2: Now test context with known session
      console.log('\n🔍 Test 2: Testing /context with known session...');
      
      // Wait a moment for data to be indexed
      console.log('   Waiting 2 seconds for indexing...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Get a system prompt to test with
      const listResult = await makeRequest('/list-system-prompts');
      if (listResult.ok && listResult.data.keys.length > 0) {
        const testPrompt = listResult.data.keys[0];
        
        const contextResult = await makeRequest('/context', 'POST', {
          system_prompt: testPrompt,
          session_id: "diagnostic-session-123"
        });
        
        if (contextResult.ok) {
          console.log(`✅ Context endpoint working with real data!`);
          console.log(`   Context length: ${contextResult.data.context?.length || 0}`);
        } else {
          console.log(`❌ Context still failing: ${contextResult.status}`);
          
          // More detailed error analysis
          if (contextResult.status === 500) {
            console.log(`\n💡 500 Error Analysis:`);
            console.log(`   This suggests an internal server error in the worker`);
            console.log(`   Most likely causes:`);
            console.log(`   1. getEmbedding() function throwing an exception`);
            console.log(`   2. Vectorize query failing`);
            console.log(`   3. Memory limit exceeded`);
            console.log(`   4. Timeout during OpenAI API call`);
          }
        }
      }
      
    } else {
      console.log(`❌ Embedding generation failed: ${ingestResult.status}`);
      console.log(`   This suggests OpenAI API issues`);
    }
    
  } catch (error) {
    console.log(`❌ Embedding flow test failed: ${error.message}`);
  }
}

async function suggestFixes() {
  console.log('\n💡 Suggested Fixes for /context Endpoint');
  console.log('========================================');
  
  console.log(`\n🔧 Immediate fixes to try:`);
  console.log(`\n1. Add error handling to /context endpoint:`);
  console.log(`   - Wrap getEmbedding() call in try-catch`);
  console.log(`   - Add timeout handling for OpenAI API calls`);
  console.log(`   - Return proper JSON error responses`);
  
  console.log(`\n2. Check OpenAI API configuration:`);
  console.log(`   - Verify API key is valid and has credits`);
  console.log(`   - Check rate limiting`);
  console.log(`   - Test with shorter system prompts`);
  
  console.log(`\n3. Debug Vectorize integration:`);
  console.log(`   - Add logging to Vectorize query`);
  console.log(`   - Check if index exists and is properly configured`);
  console.log(`   - Verify session_id filtering works`);
  
  console.log(`\n4. Worker configuration:`);
  console.log(`   - Check memory limits in wrangler.toml`);
  console.log(`   - Verify all environment variables are set`);
  console.log(`   - Check worker logs in Cloudflare dashboard`);
  
  console.log(`\n📝 Code fix example:`);
  console.log(`\n   // Add this error handling to /context endpoint:`);
  console.log(`   try {`);
  console.log(`     const embedding = await getEmbedding(systemPromptKV, env);`);
  console.log(`     // ... rest of the code`);
  console.log(`   } catch (error) {`);
  console.log(`     console.error('Context endpoint error:', error);`);
  console.log(`     return new Response(JSON.stringify({`);
  console.log(`       error: 'Internal server error',`);
  console.log(`       details: error.message`);
  console.log(`     }), { status: 500, headers: { 'Content-Type': 'application/json' } });`);
  console.log(`   }`);
}

async function runDiagnosis() {
  console.log('🚀 Context Endpoint Diagnosis');
  console.log('=============================');
  console.log(`Target URL: ${BASE_URL}`);
  
  await diagnoseContextEndpoint();
  await testEmbeddingFlow();
  await suggestFixes();
  
  console.log('\n🎯 Summary');
  console.log('==========');
  console.log('✅ /ingest endpoint: Working (embedding generation OK)');
  console.log('✅ System prompt management: Working');
  console.log('❌ /context endpoint: 500 error (needs debugging)');
  
  console.log('\n🔍 Next steps:');
  console.log('1. Check Cloudflare Worker logs for detailed error messages');
  console.log('2. Add error handling to /context endpoint');
  console.log('3. Test with shorter system prompts');
  console.log('4. Verify Vectorize index configuration');
}

if (require.main === module) {
  runDiagnosis();
}

module.exports = { runDiagnosis, diagnoseContextEndpoint, testEmbeddingFlow, suggestFixes };
