{"name": "or", "description": "Returns an element from an array when it matches a condition", "version": "0.2.0", "keywords": ["array", "utils"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://margaine.com"}, "main": "lib/or", "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/Ralt/or.git"}, "bugs": {"url": "https://github.com/Ralt/or/issues"}}