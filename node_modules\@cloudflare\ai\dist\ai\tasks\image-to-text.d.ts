import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiImageToTextInput, BaseAiImageToText } from "./types/tasks";
export declare class AiImageToText extends BaseAiImageToText implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            oneOf: ({
                type: string;
                format: string;
                properties?: undefined;
                required?: undefined;
                not?: undefined;
                errorMessage?: undefined;
            } | {
                type: string;
                properties: {
                    temperature: {
                        type: string;
                    };
                    prompt: {
                        type: string;
                    };
                    raw: {
                        type: string;
                        default: boolean;
                    };
                    messages: {
                        type: string;
                        items: {
                            type: string;
                            properties: {
                                role: {
                                    type: string;
                                };
                                content: {
                                    type: string;
                                    maxLength: number;
                                };
                            };
                            required: string[];
                        };
                    };
                    image: {
                        oneOf: ({
                            type: string;
                            items: {
                                type: string;
                            };
                            format?: undefined;
                        } | {
                            type: string;
                            format: string;
                            items?: undefined;
                        })[];
                    };
                    max_tokens: {
                        type: string;
                        default: number;
                    };
                };
                required: string[];
                not: {
                    required: string[];
                };
                errorMessage: {
                    not: string;
                };
                format?: undefined;
            })[];
        };
        output: {
            type: string;
            contentType: string;
            properties: {
                description: {
                    type: string;
                };
            };
        };
    };
    constructor(inputs: AiImageToTextInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=image-to-text.d.ts.map