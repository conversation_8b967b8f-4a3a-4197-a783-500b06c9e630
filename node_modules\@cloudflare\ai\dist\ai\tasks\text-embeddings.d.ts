import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiTextEmbeddingsInput, BaseAiTextEmbeddings } from "./types/tasks";
export declare class AiTextEmbeddings extends BaseAiTextEmbeddings implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                text: {
                    oneOf: ({
                        type: string;
                        minLength: number;
                        items?: undefined;
                        maxItems?: undefined;
                    } | {
                        type: string;
                        items: {
                            type: string;
                            minLength: number;
                        };
                        maxItems: number;
                        minLength?: undefined;
                    })[];
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            properties: {
                shape: {
                    type: string;
                    items: {
                        type: string;
                    };
                };
                data: {
                    type: string;
                    items: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                };
            };
        };
    };
    constructor(inputs: AiTextEmbeddingsInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=text-embeddings.d.ts.map