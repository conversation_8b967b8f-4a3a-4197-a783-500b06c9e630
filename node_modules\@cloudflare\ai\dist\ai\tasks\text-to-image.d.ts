import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiTextToImageInput, BaseAiTextToImage } from "./types/tasks";
export declare class AiTextToImage extends BaseAiTextToImage implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                prompt: {
                    type: string;
                    minLength: number;
                };
                image: {
                    type: string;
                    items: {
                        type: string;
                    };
                };
                mask: {
                    type: string;
                    items: {
                        type: string;
                    };
                };
                num_steps: {
                    type: string;
                    default: number;
                    maximum: number;
                };
                strength: {
                    type: string;
                    default: number;
                };
                guidance: {
                    type: string;
                    default: number;
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            format: string;
        };
    };
    constructor(inputs: AiTextToImageInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    OldgenerateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=text-to-image.d.ts.map