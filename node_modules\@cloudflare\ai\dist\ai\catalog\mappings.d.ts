import { AiImageClassification } from "../tasks/image-classification";
import { AiImageToText } from "../tasks/image-to-text";
import { AiObjectDetection } from "../tasks/object-detection";
import { AiSentenceSimilarity } from "../tasks/sentence-similarity";
import { AiSpeechRecognition } from "../tasks/speech-recognition";
import { AiSummarization } from "../tasks/summarization";
import { AiTextClassification } from "../tasks/text-classification";
import { AiTextEmbeddings } from "../tasks/text-embeddings";
import { AiTextGeneration } from "../tasks/text-generation";
import { AiTextToImage } from "../tasks/text-to-image";
import { AiTranslation } from "../tasks/translation";
import { BaseAiImageClassification, BaseAiImageToText, BaseAiObjectDetection, BaseAiSentenceSimilarity, BaseAiSpeechRecognition, BaseAiSummarization, BaseAiTextClassification, BaseAiTextEmbeddings, BaseAiTextGeneration, BaseAiTextToImage, BaseAiTranslation } from "../tasks/types/tasks";
export declare const modelMappings: {
    readonly "text-classification": {
        readonly models: readonly ["@cf/huggingface/distilbert-sst-2-int8", "@cf/jpmorganchase/roberta-spam", "@cf/inml/inml-roberta-dga"];
        readonly class: typeof AiTextClassification;
        readonly baseClass: typeof BaseAiTextClassification;
    };
    readonly "text-to-image": {
        readonly models: readonly ["@cf/stabilityai/stable-diffusion-xl-base-1.0", "@cf/stabilityai/stable-diffusion-xl-turbo", "@cf/runwayml/stable-diffusion-v1-5-inpainting", "@cf/runwayml/stable-diffusion-v1-5-img2img", "@cf/lykon/dreamshaper-8-lcm", "@cf/bytedance/stable-diffusion-xl-lightning"];
        readonly class: typeof AiTextToImage;
        readonly baseClass: typeof BaseAiTextToImage;
    };
    readonly "sentence-similarity": {
        readonly models: readonly ["@hf/sentence-transformers/all-minilm-l6-v2"];
        readonly class: typeof AiSentenceSimilarity;
        readonly baseClass: typeof BaseAiSentenceSimilarity;
    };
    readonly "text-embeddings": {
        readonly models: readonly ["@cf/baai/bge-small-en-v1.5", "@cf/baai/bge-base-en-v1.5", "@cf/baai/bge-large-en-v1.5"];
        readonly class: typeof AiTextEmbeddings;
        readonly baseClass: typeof BaseAiTextEmbeddings;
    };
    readonly "speech-recognition": {
        readonly models: readonly ["@cf/openai/whisper", "@cf/openai/whisper-tiny-en", "@cf/openai/whisper-sherpa"];
        readonly class: typeof AiSpeechRecognition;
        readonly baseClass: typeof BaseAiSpeechRecognition;
    };
    readonly "image-classification": {
        readonly models: readonly ["@cf/microsoft/resnet-50"];
        readonly class: typeof AiImageClassification;
        readonly baseClass: typeof BaseAiImageClassification;
    };
    readonly "object-detection": {
        readonly models: readonly ["@cf/facebook/detr-resnet-50"];
        readonly class: typeof AiObjectDetection;
        readonly baseClass: typeof BaseAiObjectDetection;
    };
    readonly "text-generation": {
        readonly models: readonly ["@cf/meta/llama-3-8b-instruct", "@cf/meta/llama-2-7b-chat-int8", "@cf/mistral/mistral-7b-instruct-v0.1", "@cf/mistral/mistral-7b-instruct-v0.1-vllm", "@cf/mistral/mistral-7b-instruct-v0.2-lora", "@cf/meta/llama-2-7b-chat-fp16", "@hf/thebloke/llama-2-13b-chat-awq", "@hf/thebloke/zephyr-7b-beta-awq", "@hf/thebloke/mistral-7b-instruct-v0.1-awq", "@hf/thebloke/codellama-7b-instruct-awq", "@hf/thebloke/openchat_3.5-awq", "@hf/thebloke/openhermes-2.5-mistral-7b-awq", "@hf/thebloke/neural-chat-7b-v3-1-awq", "@hf/thebloke/llamaguard-7b-awq", "@hf/thebloke/deepseek-coder-6.7b-base-awq", "@hf/thebloke/deepseek-coder-6.7b-instruct-awq", "@hf/nousresearch/hermes-2-pro-mistral-7b", "@hf/mistral/mistral-7b-instruct-v0.2", "@cf/mistral/mixtral-8x7b-instruct-v0.1-awq", "@hf/google/gemma-7b-it", "@hf/nexusflow/starling-lm-7b-beta", "@cf/deepseek-ai/deepseek-math-7b-instruct", "@cf/defog/sqlcoder-7b-2", "@cf/openchat/openchat-3.5-0106", "@cf/tiiuae/falcon-7b-instruct", "@cf/thebloke/discolm-german-7b-v1-awq", "@cf/qwen/qwen1.5-0.5b-chat", "@cf/qwen/qwen1.5-1.8b-chat", "@cf/qwen/qwen1.5-7b-chat-awq", "@cf/qwen/qwen1.5-14b-chat-awq", "@cf/tinyllama/tinyllama-1.1b-chat-v1.0", "@cf/microsoft/phi-2", "@cf/google/gemma-2b-it-lora", "@cf/google/gemma-7b-it-lora", "@cf/meta-llama/llama-2-7b-chat-hf-lora", "@cf/deepseek-ai/deepseek-coder-7b-instruct-v1.5", "@cf/nexaaidev/octopus-v2", "@cf/m-a-p/opencodeinterpreter-ds-6.7b", "@cf/fblgit/una-cybertron-7b-v2-bf16", "@cf/sven/test"];
        readonly class: typeof AiTextGeneration;
        readonly baseClass: typeof BaseAiTextGeneration;
    };
    readonly translation: {
        readonly models: readonly ["@cf/meta/m2m100-1.2b"];
        readonly class: typeof AiTranslation;
        readonly baseClass: typeof BaseAiTranslation;
    };
    readonly summarization: {
        readonly models: readonly ["@cf/facebook/bart-large-cnn"];
        readonly class: typeof AiSummarization;
        readonly baseClass: typeof BaseAiSummarization;
    };
    readonly "image-to-text": {
        readonly models: readonly ["@cf/unum/uform-gen2-qwen-500m", "@cf/llava-hf/llava-1.5-7b-hf"];
        readonly class: typeof AiImageToText;
        readonly baseClass: typeof BaseAiImageToText;
    };
};
//# sourceMappingURL=mappings.d.ts.map
