name = "queue-logger-worker"
main = "log/worker.ts"
compatibility_date = "2024-05-07"
account_id = "a664197d92717007b1b273365770c296"

[vars]
CLAUDE_API_KEY = "************************************************************************************************************"
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
GOOGLE_VISION_KEY = "AIzaSyAkFsdk82LtSft-awwCV_n9y6L_MaCa7rI"
CF_ACCOUNT_ID = 'a664197d92717007b1b273365770c296'
CF_API_TOKEN = 'PCjviy8rt18Hg3_T627K8F1-GA7X7ovf7e_tr-w4'
CF_AI_GATEWAY_KEY = "cMavy3N0SpfiiqxiUduh56l0k5L6Tb8HMQ9L-OFV"

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true

[[kv_namespaces]]
binding = "TASK_STATUS"
id = "27bc5c70d84845a682da8d78fdca14d1"
preview_id = "4d2dc67d4beb4c6da07e60fe8deb273e"

[[kv_namespaces]]
binding = "VECTOR_KV"
id = "246e030b04494b69b0deb21f9e1ed4fc"

[[queues.consumers]]
queue = "workflow-tasks-log"

[[r2_buckets]]
preview_bucket_name = "agent-ai"
bucket_name = "agent-ai"
binding = "BUCKET_AI"

[[kv_namespaces]]
binding = "PROMPT"
id = "a799d367ffa8463398f44dec32773966"
preview_id = "fe5387e1b44740dc92ccf673db9d57d7"

[limits]
cpu_ms = 300_000
