export type AiImageClassificationInput = {
    image: number[];
};
export type AiImageClassificationOutput = {
    score?: number;
    label?: string;
}[];
export declare abstract class BaseAiImageClassification {
    inputs: AiImageClassificationInput;
    postProcessedOutputs: AiImageClassificationOutput;
}
export type AiImageToTextInput = {
    image: number[];
    prompt?: string;
    max_tokens?: number;
    temperature?: number;
    raw?: boolean;
    messages?: RoleScopedChatInput[];
};
export type AiImageToTextOutput = {
    description: string;
};
export declare abstract class BaseAiImageToText {
    inputs: AiImageToTextInput;
    postProcessedOutputs: AiImageToTextOutput;
}
export type AiObjectDetectionInput = {
    image: number[];
};
export type AiObjectDetectionOutput = {
    score?: number;
    label?: string;
}[];
export declare abstract class BaseAiObjectDetection {
    inputs: AiObjectDetectionInput;
    postProcessedOutputs: AiObjectDetectionOutput;
}
export type AiSentenceSimilarityInput = {
    source: string;
    sentences: string[];
};
export type AiSentenceSimilarityOutput = number[];
export declare abstract class BaseAiSentenceSimilarity {
    inputs: AiSentenceSimilarityInput;
    postProcessedOutputs: AiSentenceSimilarityOutput;
}
export type AiSpeechRecognitionInput = {
    audio: number[];
};
export type AiSpeechRecognitionOutput = {
    text?: string;
    words?: {
        word: string;
        start: number;
        end: number;
    }[];
    vtt?: string;
};
export declare abstract class BaseAiSpeechRecognition {
    inputs: AiSpeechRecognitionInput;
    postProcessedOutputs: AiSpeechRecognitionOutput;
}
export type AiSummarizationInput = {
    input_text: string;
    max_length?: number;
};
export type AiSummarizationOutput = {
    summary: string;
};
export declare abstract class BaseAiSummarization {
    inputs: AiSummarizationInput;
    postProcessedOutputs: AiSummarizationOutput;
}
export type AiTextClassificationInput = {
    text: string;
};
export type AiTextClassificationOutput = {
    score?: number;
    label?: string;
}[];
export declare abstract class BaseAiTextClassification {
    inputs: AiTextClassificationInput;
    postProcessedOutputs: AiTextClassificationOutput;
}
export type AiTextEmbeddingsInput = {
    text: string | string[];
};
export type AiTextEmbeddingsOutput = {
    shape: number[];
    data: number[][];
};
export declare abstract class BaseAiTextEmbeddings {
    inputs: AiTextEmbeddingsInput;
    postProcessedOutputs: AiTextEmbeddingsOutput;
}
export type RoleScopedChatInput = {
    role: string;
    content: string;
};
export type AiTextGenerationInput = {
    prompt?: string;
    raw?: boolean;
    stream?: boolean;
    max_tokens?: number;
    temperature?: number;
    messages?: RoleScopedChatInput[];
};
export type AiTextGenerationOutput = {
    response?: string;
} | ReadableStream;
export declare abstract class BaseAiTextGeneration {
    inputs: AiTextGenerationInput;
    postProcessedOutputs: AiTextGenerationOutput;
}
export type AiTextToImageInput = {
    prompt: string;
    image?: number[];
    mask?: number[];
    num_steps?: number;
    strength?: number;
    guidance?: number;
};
export type AiTextToImageOutput = Uint8Array;
export declare abstract class BaseAiTextToImage {
    inputs: AiTextToImageInput;
    postProcessedOutputs: AiTextToImageOutput;
}
export type AiTranslationInput = {
    text: string;
    target_lang: string;
    source_lang?: string;
};
export type AiTranslationOutput = {
    translated_text?: string;
};
export declare abstract class BaseAiTranslation {
    inputs: AiTranslationInput;
    postProcessedOutputs: AiTranslationOutput;
}
//# sourceMappingURL=tasks.d.ts.map