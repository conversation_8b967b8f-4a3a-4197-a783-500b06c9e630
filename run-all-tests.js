// Comprehensive test runner for all embedding tests
// Run with: node run-all-tests.js [test-type]

const { spawn } = require('child_process');
const path = require('path');

// Test configurations
const tests = {
  mock: {
    file: 'test-embedding-mock.js',
    description: 'Mock tests (no API key required)',
    env: {},
    required: []
  },
  real: {
    file: 'test-embedding-real.js', 
    description: 'Real OpenAI API tests',
    env: {},
    required: ['OPENAI_API_KEY']
  },
  worker: {
    file: 'test-worker-endpoints.js',
    description: 'Cloudflare Worker endpoint tests',
    env: {},
    required: ['BASE_URL']
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Check environment variables
function checkEnvironment(testType) {
  const test = tests[testType];
  const missing = [];
  
  for (const envVar of test.required) {
    if (!process.env[envVar]) {
      missing.push(envVar);
    }
  }
  
  return missing;
}

// Run a single test
function runTest(testType) {
  return new Promise((resolve, reject) => {
    const test = tests[testType];
    const testFile = path.join(__dirname, test.file);
    
    console.log(colorize(`\n🚀 Running ${testType} tests...`, 'cyan'));
    console.log(colorize(`📄 File: ${test.file}`, 'blue'));
    console.log(colorize(`📝 Description: ${test.description}`, 'blue'));
    
    // Check required environment variables
    const missing = checkEnvironment(testType);
    if (missing.length > 0) {
      console.log(colorize(`⚠️  Missing environment variables: ${missing.join(', ')}`, 'yellow'));
      
      if (testType === 'real') {
        console.log(colorize('💡 Set OPENAI_API_KEY to run real API tests', 'yellow'));
      }
      if (testType === 'worker') {
        console.log(colorize('💡 Set BASE_URL to your deployed worker URL', 'yellow'));
      }
      
      resolve({ testType, status: 'skipped', reason: `Missing: ${missing.join(', ')}` });
      return;
    }
    
    const startTime = Date.now();
    
    // Spawn child process to run the test
    const child = spawn('node', [testFile], {
      stdio: 'pipe',
      env: { ...process.env, ...test.env }
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      process.stdout.write(output); // Real-time output
    });
    
    child.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      process.stderr.write(colorize(output, 'red')); // Real-time error output
    });
    
    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      
      if (code === 0) {
        console.log(colorize(`✅ ${testType} tests completed successfully in ${duration}ms`, 'green'));
        resolve({ 
          testType, 
          status: 'passed', 
          duration, 
          stdout, 
          stderr 
        });
      } else {
        console.log(colorize(`❌ ${testType} tests failed with exit code ${code}`, 'red'));
        resolve({ 
          testType, 
          status: 'failed', 
          duration, 
          exitCode: code, 
          stdout, 
          stderr 
        });
      }
    });
    
    child.on('error', (error) => {
      console.error(colorize(`💥 Failed to start ${testType} tests: ${error.message}`, 'red'));
      reject(error);
    });
  });
}

// Display help information
function showHelp() {
  console.log(colorize('🧪 Embedding Test Suite', 'bright'));
  console.log('========================\n');
  
  console.log('Usage: node run-all-tests.js [test-type]\n');
  
  console.log('Available test types:');
  Object.entries(tests).forEach(([key, test]) => {
    console.log(`  ${colorize(key.padEnd(8), 'cyan')} - ${test.description}`);
    if (test.required.length > 0) {
      console.log(`            Requires: ${test.required.join(', ')}`);
    }
  });
  
  console.log('\nExamples:');
  console.log('  node run-all-tests.js mock     # Run only mock tests');
  console.log('  node run-all-tests.js real     # Run only real API tests');
  console.log('  node run-all-tests.js worker   # Run only worker endpoint tests');
  console.log('  node run-all-tests.js          # Run all available tests');
  
  console.log('\nEnvironment Variables:');
  console.log('  OPENAI_API_KEY  - Required for real API tests');
  console.log('  BASE_URL        - Required for worker tests (e.g., https://your-worker.workers.dev)');
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  // Show help if requested
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  console.log(colorize('🧪 Embedding Test Suite Runner', 'bright'));
  console.log(colorize('================================', 'bright'));
  
  // Determine which tests to run
  let testsToRun = [];
  
  if (args.length === 0) {
    // Run all tests
    testsToRun = Object.keys(tests);
    console.log(colorize('Running all available tests...', 'cyan'));
  } else {
    // Run specific test
    const testType = args[0];
    if (tests[testType]) {
      testsToRun = [testType];
      console.log(colorize(`Running ${testType} tests only...`, 'cyan'));
    } else {
      console.error(colorize(`❌ Unknown test type: ${testType}`, 'red'));
      console.log('\nAvailable test types:', Object.keys(tests).join(', '));
      process.exit(1);
    }
  }
  
  // Check environment for all tests
  console.log(colorize('\n🔍 Environment Check:', 'blue'));
  for (const testType of testsToRun) {
    const missing = checkEnvironment(testType);
    if (missing.length === 0) {
      console.log(colorize(`✅ ${testType}: All required variables present`, 'green'));
    } else {
      console.log(colorize(`⚠️  ${testType}: Missing ${missing.join(', ')}`, 'yellow'));
    }
  }
  
  // Run tests
  const results = [];
  const startTime = Date.now();
  
  for (const testType of testsToRun) {
    try {
      const result = await runTest(testType);
      results.push(result);
    } catch (error) {
      console.error(colorize(`💥 Failed to run ${testType} tests: ${error.message}`, 'red'));
      results.push({ testType, status: 'error', error: error.message });
    }
  }
  
  // Summary
  const totalDuration = Date.now() - startTime;
  
  console.log(colorize('\n📊 Test Summary', 'bright'));
  console.log(colorize('===============', 'bright'));
  
  const passed = results.filter(r => r.status === 'passed').length;
  const failed = results.filter(r => r.status === 'failed').length;
  const skipped = results.filter(r => r.status === 'skipped').length;
  const errors = results.filter(r => r.status === 'error').length;
  
  console.log(`Total tests run: ${results.length}`);
  console.log(colorize(`✅ Passed: ${passed}`, 'green'));
  console.log(colorize(`❌ Failed: ${failed}`, failed > 0 ? 'red' : 'green'));
  console.log(colorize(`⏭️  Skipped: ${skipped}`, skipped > 0 ? 'yellow' : 'green'));
  console.log(colorize(`💥 Errors: ${errors}`, errors > 0 ? 'red' : 'green'));
  console.log(`⏱️  Total duration: ${totalDuration}ms`);
  
  // Detailed results
  console.log(colorize('\n📋 Detailed Results:', 'blue'));
  results.forEach(result => {
    const statusColor = {
      'passed': 'green',
      'failed': 'red', 
      'skipped': 'yellow',
      'error': 'red'
    }[result.status];
    
    console.log(`${colorize(result.testType.padEnd(8), 'cyan')}: ${colorize(result.status.toUpperCase(), statusColor)}`);
    
    if (result.duration) {
      console.log(`          Duration: ${result.duration}ms`);
    }
    if (result.reason) {
      console.log(`          Reason: ${result.reason}`);
    }
    if (result.exitCode) {
      console.log(`          Exit code: ${result.exitCode}`);
    }
  });
  
  // Recommendations
  if (skipped > 0 || failed > 0 || errors > 0) {
    console.log(colorize('\n💡 Recommendations:', 'yellow'));
    
    if (skipped > 0) {
      console.log('• Set missing environment variables to run skipped tests');
    }
    if (failed > 0 || errors > 0) {
      console.log('• Check the detailed output above for specific error messages');
      console.log('• Verify API keys and worker URLs are correct');
      console.log('• Ensure all dependencies are installed (npm install)');
    }
  }
  
  // Exit with appropriate code
  if (failed > 0 || errors > 0) {
    process.exit(1);
  } else {
    console.log(colorize('\n🎉 All tests completed successfully!', 'green'));
    process.exit(0);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(colorize(`💥 Uncaught exception: ${error.message}`, 'red'));
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(colorize(`💥 Unhandled rejection at: ${promise}`, 'red'));
  console.error(colorize(`Reason: ${reason}`, 'red'));
  process.exit(1);
});

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = {
  main,
  runTest,
  tests,
  checkEnvironment
};
