// Focused test for the /context endpoint
const crypto = require('crypto');

const BASE_URL = 'https://ai.klikbuy.co.id';
const TEST_SESSION_ID = `test-session-${Date.now()}`;

async function makeRequest(endpoint, method = 'GET', body = null) {
  const url = `${BASE_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  console.log(`🔄 ${method} ${endpoint}`);
  if (body) {
    console.log(`   Body: ${JSON.stringify(body, null, 2)}`);
  }
  
  try {
    const response = await fetch(url, options);
    const responseText = await response.text();
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (typeof responseData === 'string' && responseData.length > 200) {
      console.log(`   Response: "${responseData.substring(0, 200)}..."`);
    } else {
      console.log(`   Response: ${JSON.stringify(responseData, null, 2)}`);
    }
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      ok: response.ok
    };
    
  } catch (error) {
    console.error(`❌ Request failed: ${error.message}`);
    throw error;
  }
}

async function testContextEndpoint() {
  console.log('🧪 Testing /context Endpoint');
  console.log('============================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Session ID: ${TEST_SESSION_ID}`);
  
  // First, let's add some data to ingest
  console.log('\n📥 Step 1: Adding test data...');
  
  const testData = {
    text: "This is test content for context retrieval testing",
    session_id: TEST_SESSION_ID,
    agent: "test-agent"
  };
  
  try {
    const ingestResult = await makeRequest('/ingest', 'POST', testData);
    
    if (ingestResult.ok) {
      console.log(`✅ Test data ingested successfully: ${ingestResult.data.id}`);
    } else {
      console.log(`⚠️  Failed to ingest test data: ${ingestResult.data}`);
    }
  } catch (error) {
    console.log(`⚠️  Ingest failed: ${error.message}`);
  }
  
  // Wait a moment for the data to be processed
  console.log('\n⏳ Waiting 2 seconds for data processing...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test context retrieval with different system prompts
  console.log('\n📤 Step 2: Testing context retrieval...');
  
  const testCases = [
    {
      system_prompt: 'prompt_audience_analysis',
      session_id: TEST_SESSION_ID,
      description: 'Audience analysis prompt'
    },
    {
      system_prompt: 'prompt-audience-profiling',
      session_id: TEST_SESSION_ID,
      description: 'Audience profiling prompt'
    },
    {
      system_prompt: 'cta_button_mapping',
      session_id: TEST_SESSION_ID,
      description: 'CTA button mapping prompt'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.description}`);
    console.log(`System Prompt: ${testCase.system_prompt}`);
    
    try {
      const result = await makeRequest('/context', 'POST', {
        system_prompt: testCase.system_prompt,
        session_id: testCase.session_id
      });
      
      if (result.ok) {
        console.log(`✅ Context retrieval successful!`);
        console.log(`   Context length: ${result.data.context?.length || 0} characters`);
        
        if (result.data.context && result.data.context.length > 0) {
          console.log(`   Context preview: "${result.data.context.substring(0, 100)}..."`);
        } else {
          console.log(`   ⚠️  Empty context returned`);
        }
      } else {
        console.log(`❌ Context retrieval failed: ${result.status}`);
        
        if (result.status === 404) {
          console.log(`   💡 System prompt '${testCase.system_prompt}' not found in KV store`);
        } else if (result.status === 500) {
          console.log(`   💡 Internal server error - check worker logs`);
          
          // If it's HTML error page, extract useful info
          if (typeof result.data === 'string' && result.data.includes('Worker threw exception')) {
            console.log(`   💡 Worker exception occurred`);
          }
        }
      }
      
    } catch (error) {
      console.log(`❌ Request failed: ${error.message}`);
    }
    
    // Delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Test with a system prompt that should exist
  console.log('\n🔍 Step 3: Testing with known system prompt...');
  
  try {
    // First check what prompts are available
    const listResult = await makeRequest('/list-system-prompts');
    
    if (listResult.ok && listResult.data.keys && listResult.data.keys.length > 0) {
      const availablePrompt = listResult.data.keys[0];
      console.log(`Using available prompt: ${availablePrompt}`);
      
      const result = await makeRequest('/context', 'POST', {
        system_prompt: availablePrompt,
        session_id: TEST_SESSION_ID
      });
      
      if (result.ok) {
        console.log(`✅ Context retrieval with known prompt successful!`);
        console.log(`   Context length: ${result.data.context?.length || 0} characters`);
      } else {
        console.log(`❌ Context retrieval with known prompt failed: ${result.status}`);
      }
    } else {
      console.log(`⚠️  No system prompts available`);
    }
    
  } catch (error) {
    console.log(`❌ Known prompt test failed: ${error.message}`);
  }
}

async function testSystemPromptFirst() {
  console.log('\n🧪 Testing System Prompt Retrieval');
  console.log('==================================');
  
  try {
    // Check if a specific prompt exists
    const testPrompt = 'prompt_audience_analysis';
    console.log(`Checking if '${testPrompt}' exists...`);
    
    const result = await makeRequest(`/get-system-prompt?agent=${testPrompt}`);
    
    if (result.ok) {
      console.log(`✅ System prompt found!`);
      console.log(`   Prompt length: ${result.data.prompt.length} characters`);
      console.log(`   Prompt preview: "${result.data.prompt.substring(0, 100)}..."`);
      
      // Now test context with this prompt
      console.log(`\n🔄 Testing context with this prompt...`);
      
      const contextResult = await makeRequest('/context', 'POST', {
        system_prompt: testPrompt,
        session_id: TEST_SESSION_ID
      });
      
      if (contextResult.ok) {
        console.log(`✅ Context retrieval successful!`);
      } else {
        console.log(`❌ Context retrieval failed: ${contextResult.status}`);
        console.log(`   Error details: ${JSON.stringify(contextResult.data)}`);
      }
      
    } else {
      console.log(`❌ System prompt not found: ${result.status}`);
    }
    
  } catch (error) {
    console.log(`❌ System prompt test failed: ${error.message}`);
  }
}

async function runTest() {
  console.log('🚀 Context Endpoint Focused Test');
  console.log('================================');
  
  await testSystemPromptFirst();
  await testContextEndpoint();
  
  console.log('\n🎉 Context endpoint testing completed!');
}

if (require.main === module) {
  runTest();
}

module.exports = { runTest, testContextEndpoint, testSystemPromptFirst };
