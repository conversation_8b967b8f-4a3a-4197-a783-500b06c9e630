// Test script for <PERSON>flare Worker embedding endpoints
// Run with: BASE_URL=https://your-worker.workers.dev node test-worker-endpoints.js

const crypto = require('crypto');

// Configuration
const config = {
  BASE_URL: process.env.BASE_URL || 'http://localhost:8787',
  TEST_SESSION_ID: `test-session-${Date.now()}`,
  TEST_AGENT: 'test-agent',
  TIMEOUT: 30000 // 30 seconds timeout
};

// HTTP request helper with timeout
async function makeRequest(endpoint, method = 'GET', body = null, timeout = config.TIMEOUT) {
  const url = `${config.BASE_URL}${endpoint}`;
  
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
    signal: controller.signal
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  console.log(`🔄 ${method} ${endpoint}`);
  if (body) {
    console.log(`   Body: ${JSON.stringify(body, null, 2)}`);
  }
  
  try {
    const response = await fetch(url, options);
    clearTimeout(timeoutId);
    
    const responseText = await response.text();
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Response: ${JSON.stringify(responseData, null, 2)}`);
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    };
    
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      console.error(`❌ Request timeout after ${timeout}ms`);
    } else {
      console.error(`❌ Request failed: ${error.message}`);
    }
    throw error;
  }
}

// Test 1: /ingest endpoint
async function testIngestEndpoint() {
  console.log('\n🧪 Test 1: /ingest Endpoint');
  console.log('===========================');
  
  const testDocuments = [
    {
      text: "Machine learning adalah subset dari artificial intelligence yang memungkinkan komputer untuk belajar dan membuat keputusan tanpa diprogram secara eksplisit.",
      session_id: config.TEST_SESSION_ID,
      agent: "ml-agent"
    },
    {
      text: "Deep learning menggunakan neural networks dengan banyak layer untuk memproses data kompleks seperti gambar, teks, dan audio dengan akurasi tinggi.",
      session_id: config.TEST_SESSION_ID,
      agent: "dl-agent"
    },
    {
      text: "Cloudflare Workers adalah platform serverless yang memungkinkan developer menjalankan JavaScript di edge locations untuk performa yang optimal.",
      session_id: config.TEST_SESSION_ID,
      agent: "cloudflare-agent"
    },
    {
      text: "Vector databases seperti Vectorize memungkinkan penyimpanan dan pencarian embedding dengan similarity search yang sangat efisien dan scalable.",
      session_id: config.TEST_SESSION_ID,
      agent: "vector-agent"
    }
  ];
  
  const ingestResults = [];
  
  for (let i = 0; i < testDocuments.length; i++) {
    const doc = testDocuments[i];
    console.log(`\nIngesting document ${i + 1}/${testDocuments.length}:`);
    console.log(`Text: "${doc.text.substring(0, 80)}..."`);
    console.log(`Session: ${doc.session_id}`);
    console.log(`Agent: ${doc.agent}`);
    
    try {
      const startTime = Date.now();
      const result = await makeRequest('/ingest', 'POST', doc);
      const duration = Date.now() - startTime;
      
      if (result.ok) {
        console.log(`✅ Ingest successful in ${duration}ms`);
        console.log(`   Vector ID: ${result.data.id}`);
        console.log(`   Status: ${result.data.status}`);
        
        // Validate response format
        if (!result.data.id || result.data.status !== 'ok') {
          console.log(`⚠️  Unexpected response format`);
        }
        
        ingestResults.push({
          ...doc,
          vectorId: result.data.id,
          duration,
          success: true
        });
        
      } else {
        console.log(`❌ Ingest failed with status ${result.status}`);
        console.log(`   Error: ${JSON.stringify(result.data)}`);
        
        ingestResults.push({
          ...doc,
          error: result.data,
          duration,
          success: false
        });
      }
      
    } catch (error) {
      console.error(`❌ Ingest request failed: ${error.message}`);
      ingestResults.push({
        ...doc,
        error: error.message,
        success: false
      });
    }
    
    // Delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  const successCount = ingestResults.filter(r => r.success).length;
  console.log(`\n📊 Ingest Summary:`);
  console.log(`   ✅ Successful: ${successCount}/${testDocuments.length}`);
  console.log(`   ❌ Failed: ${testDocuments.length - successCount}/${testDocuments.length}`);
  
  if (successCount > 0) {
    const avgDuration = ingestResults
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.duration, 0) / successCount;
    console.log(`   ⏱️  Average duration: ${avgDuration.toFixed(0)}ms`);
  }
  
  return ingestResults;
}

// Test 2: System prompt management
async function testSystemPromptManagement() {
  console.log('\n🧪 Test 2: System Prompt Management');
  console.log('===================================');
  
  const testPromptKey = `test-prompt-${Date.now()}`;
  const testPromptValue = "You are an AI assistant specialized in analyzing customer data and providing actionable insights for marketing campaigns. Focus on demographic analysis, behavioral patterns, and recommendation generation.";
  
  try {
    // Test add system prompt
    console.log('\nTesting add system prompt...');
    const addData = new URLSearchParams({
      agent: testPromptKey,
      prompt: testPromptValue
    });
    
    const addResponse = await fetch(`${config.BASE_URL}/add-system-prompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: addData
    });
    
    const addResult = await addResponse.json();
    console.log(`Add result: ${JSON.stringify(addResult)}`);
    
    if (addResponse.ok && addResult.status === 'ok') {
      console.log('✅ System prompt added successfully');
      
      // Test get system prompt
      console.log('\nTesting get system prompt...');
      const getResult = await makeRequest(`/get-system-prompt?agent=${testPromptKey}`);
      
      if (getResult.ok) {
        console.log('✅ System prompt retrieved successfully');
        console.log(`   Agent: ${getResult.data.agent}`);
        console.log(`   Prompt length: ${getResult.data.prompt.length} characters`);
        console.log(`   Prompt preview: "${getResult.data.prompt.substring(0, 100)}..."`);
        
        // Verify content matches
        if (getResult.data.prompt === testPromptValue) {
          console.log('✅ Prompt content matches exactly');
        } else {
          console.log('⚠️  Prompt content mismatch');
        }
      } else {
        console.log(`❌ Failed to retrieve system prompt: ${getResult.data}`);
      }
      
      // Test list system prompts
      console.log('\nTesting list system prompts...');
      const listResult = await makeRequest('/list-system-prompts');
      
      if (listResult.ok) {
        console.log('✅ System prompts listed successfully');
        console.log(`   Total prompts: ${listResult.data.keys?.length || 0}`);
        console.log(`   Test prompt included: ${listResult.data.keys?.includes(testPromptKey)}`);
        
        if (listResult.data.keys?.length > 0) {
          console.log(`   Sample prompts: ${listResult.data.keys.slice(0, 3).join(', ')}`);
        }
      } else {
        console.log(`❌ Failed to list system prompts: ${listResult.data}`);
      }
      
      return testPromptKey;
      
    } else {
      console.log(`❌ Failed to add system prompt: ${addResult}`);
      return null;
    }
    
  } catch (error) {
    console.error(`❌ System prompt management failed: ${error.message}`);
    return null;
  }
}

// Test 3: /context endpoint
async function testContextEndpoint(testPromptKey) {
  console.log('\n🧪 Test 3: /context Endpoint');
  console.log('============================');
  
  // Test with existing system prompts
  const testCases = [
    {
      system_prompt: 'prompt_audience_analysis',
      session_id: config.TEST_SESSION_ID,
      description: 'Audience analysis prompt'
    },
    {
      system_prompt: 'prompt-audience-profiling',
      session_id: config.TEST_SESSION_ID,
      description: 'Audience profiling prompt'
    }
  ];
  
  // Add test prompt if available
  if (testPromptKey) {
    testCases.push({
      system_prompt: testPromptKey,
      session_id: config.TEST_SESSION_ID,
      description: 'Custom test prompt'
    });
  }
  
  for (const testCase of testCases) {
    console.log(`\nTesting context retrieval:`);
    console.log(`System Prompt: ${testCase.system_prompt}`);
    console.log(`Session ID: ${testCase.session_id}`);
    console.log(`Description: ${testCase.description}`);
    
    try {
      const startTime = Date.now();
      const result = await makeRequest('/context', 'POST', {
        system_prompt: testCase.system_prompt,
        session_id: testCase.session_id
      });
      const duration = Date.now() - startTime;
      
      if (result.ok) {
        console.log(`✅ Context retrieval successful in ${duration}ms`);
        console.log(`   Context length: ${result.data.context?.length || 0} characters`);
        
        if (result.data.context && result.data.context.length > 0) {
          console.log(`   Context preview: "${result.data.context.substring(0, 150)}..."`);
          
          // Count number of context segments
          const segments = result.data.context.split('\n\n').filter(s => s.trim().length > 0);
          console.log(`   Context segments: ${segments.length}`);
        } else {
          console.log(`   ⚠️  Empty context returned`);
        }
        
      } else {
        console.log(`❌ Context retrieval failed with status ${result.status}`);
        console.log(`   Error: ${JSON.stringify(result.data)}`);
        
        // Handle specific error cases
        if (result.status === 404) {
          console.log(`   💡 System prompt '${testCase.system_prompt}' not found in KV store`);
        } else if (result.status === 500) {
          console.log(`   💡 Possible embedding generation or Vectorize query issue`);
        }
      }
      
    } catch (error) {
      console.error(`❌ Context request failed: ${error.message}`);
    }
    
    // Delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Test 4: Error handling and edge cases
async function testErrorHandling() {
  console.log('\n🧪 Test 4: Error Handling');
  console.log('=========================');
  
  console.log('\nTesting missing required fields in /ingest...');
  try {
    const result = await makeRequest('/ingest', 'POST', {
      // Missing text field
      session_id: 'test',
      agent: 'test'
    });
    
    if (!result.ok) {
      console.log(`✅ Correctly rejected invalid request (${result.status})`);
    } else {
      console.log(`⚠️  Unexpected success with missing fields`);
    }
  } catch (error) {
    console.log(`✅ Request properly failed: ${error.message}`);
  }
  
  console.log('\nTesting non-existent system prompt in /context...');
  try {
    const result = await makeRequest('/context', 'POST', {
      system_prompt: 'non-existent-prompt-12345',
      session_id: config.TEST_SESSION_ID
    });
    
    if (result.status === 404) {
      console.log(`✅ Correctly returned 404 for missing system prompt`);
    } else {
      console.log(`⚠️  Unexpected status ${result.status} for missing prompt`);
    }
  } catch (error) {
    console.log(`✅ Request properly failed: ${error.message}`);
  }
  
  console.log('\nTesting invalid JSON in request body...');
  try {
    const response = await fetch(`${config.BASE_URL}/ingest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: 'invalid json content'
    });
    
    if (!response.ok) {
      console.log(`✅ Correctly rejected invalid JSON (${response.status})`);
    } else {
      console.log(`⚠️  Unexpected success with invalid JSON`);
    }
  } catch (error) {
    console.log(`✅ Request properly failed: ${error.message}`);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Cloudflare Worker Endpoint Tests');
  console.log('============================================');
  console.log(`Base URL: ${config.BASE_URL}`);
  console.log(`Test Session ID: ${config.TEST_SESSION_ID}`);
  
  // Check if worker is accessible
  try {
    console.log('\nTesting worker accessibility...');
    const healthCheck = await makeRequest('/', 'GET', null, 5000);
    console.log(`Worker responded with status: ${healthCheck.status}`);
  } catch (error) {
    console.error('❌ Worker not accessible. Please check:');
    console.error('1. BASE_URL is correct');
    console.error('2. Worker is deployed and running');
    console.error('3. Network connectivity');
    console.error(`Current BASE_URL: ${config.BASE_URL}`);
    process.exit(1);
  }
  
  try {
    const results = {};
    
    results.ingest = await testIngestEndpoint();
    results.systemPrompt = await testSystemPromptManagement();
    await testContextEndpoint(results.systemPrompt);
    await testErrorHandling();
    
    console.log('\n🎉 All Worker Endpoint Tests Completed!');
    console.log('======================================');
    console.log('📊 Test Summary:');
    
    const successfulIngests = results.ingest.filter(r => r.success).length;
    console.log(`   ✅ Ingest endpoint: ${successfulIngests}/${results.ingest.length} successful`);
    console.log(`   ✅ System prompt management: ${results.systemPrompt ? 'Working' : 'Failed'}`);
    console.log(`   ✅ Context endpoint: Tested multiple scenarios`);
    console.log(`   ✅ Error handling: Edge cases covered`);
    
    if (successfulIngests === results.ingest.length) {
      console.log('\n🎯 All embedding functionality is working correctly!');
    } else {
      console.log('\n⚠️  Some tests failed. Check the logs above for details.');
    }
    
    return results;
    
  } catch (error) {
    console.error('\n💥 Worker endpoint tests failed:', error.message);
    console.error('Stack trace:', error.stack);
    
    console.log('\n💡 Troubleshooting Tips:');
    console.log('1. Verify OPENAI_API_KEY is set in Worker environment');
    console.log('2. Check Vectorize database is properly configured');
    console.log('3. Ensure KV namespace PROMPT is bound to the Worker');
    console.log('4. Review Worker logs in Cloudflare dashboard');
    console.log('5. Verify wrangler.toml configuration');
    
    process.exit(1);
  }
}

// Run tests if executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testIngestEndpoint,
  testSystemPromptManagement,
  testContextEndpoint,
  testErrorHandling,
  makeRequest,
  config
};
