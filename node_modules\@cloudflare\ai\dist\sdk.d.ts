import { modelMappings } from "./ai/catalog";
export type AiOptions = {
    debug?: boolean;
    prefix?: string;
    extraHeaders?: object;
    overrideSettings?: object;
    fetchUrl?: string;
};
type ModelMappings = typeof modelMappings;
type GetModelName<T> = {
    [K in keyof T]: T[K] extends {
        models: readonly (infer U)[];
    } ? U : never;
}[keyof T];
type ModelName = GetModelName<ModelMappings>;
type GetModelClass<M extends ModelName, T> = {
    [K in keyof T]: T[K] extends {
        models: readonly string[];
        class: infer C;
    } ? M extends T[K]["models"][number] ? C : never : never;
}[keyof T];
type ConstructorParametersForModel<M extends ModelName> = ConstructorParameters<GetModelClass<M, ModelMappings>>[0];
type GetModelClassType<M extends ModelName> = {
    [K in keyof ModelMappings]: M extends ModelMappings[K]["models"][number] ? ModelMappings[K]["class"] : never;
}[keyof ModelMappings];
type GetModelInstanceType<M extends ModelName> = InstanceType<GetModelClassType<M>>;
type GetPostProcessedOutputsType<M extends ModelName> = GetModelInstanceType<M>["postProcessedOutputs"];
export declare class InferenceUpstreamError extends Error {
    httpCode: number;
    constructor(message: string, httpCode: number);
}
export declare class Ai {
    private binding;
    private options;
    private logs;
    lastRequestId: string;
    constructor(binding: any, options?: AiOptions);
    run<M extends ModelName>(model: M, inputs: ConstructorParametersForModel<M>): Promise<GetPostProcessedOutputsType<M>>;
    getLogs(): string[];
}
export {};
//# sourceMappingURL=sdk.d.ts.map