"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SDK_METADATA = exports.ServerList = exports.ServerEu = void 0;
exports.serverURLFromOptions = serverURLFromOptions;
const url_js_1 = require("./url.js");
/**
 * EU Production server
 */
exports.ServerEu = "eu";
/**
 * Contains the list of servers available to the SDK
 */
exports.ServerList = {
    [exports.ServerEu]: "https://api.mistral.ai",
};
function serverURLFromOptions(options) {
    let serverURL = options.serverURL;
    const params = {};
    if (!serverURL) {
        const server = options.server ?? exports.ServerEu;
        serverURL = exports.ServerList[server] || "";
    }
    const u = (0, url_js_1.pathToFunc)(serverURL)(params);
    return new URL(u);
}
exports.SDK_METADATA = {
    language: "typescript",
    openapiDocVersion: "0.0.2",
    sdkVersion: "1.6.0",
    genVersion: "2.548.6",
    userAgent: "speakeasy-sdk/typescript 1.6.0 2.548.6 0.0.2 @mistralai/mistralai",
};
//# sourceMappingURL=config.js.map