import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiSummarizationInput, BaseAiSummarization } from "./types/tasks";
export declare class AiSummarization extends BaseAiSummarization implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            properties: {
                input_text: {
                    type: string;
                    minLength: number;
                };
                max_length: {
                    type: string;
                    default: number;
                };
            };
            required: string[];
        };
        output: {
            type: string;
            contentType: string;
            properties: {
                summary: {
                    type: string;
                };
            };
        };
    };
    constructor(inputs: AiSummarizationInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
}
//# sourceMappingURL=summarization.d.ts.map