var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-aSfhnm/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/agents/product_audience_matching.ts
var systemPrompt = `Use this persona as an expert product-audience matching specialist in a multi-agent system for Facebook ad generation. Your task is to analyze how specific audience segments align with the product features and determine the most effective conversion strategies for Facebook advertising.
# Input Context
- Structured JSON data from Marketing Analysis Framework

# Methodology
## Value Proposition Alignment
- Map product benefits to audience needs with alignment scores (1-10)
- Assess price-to-value perception
- Calculate overall value proposition score

## Engagement Strategy
- Identify key engagement factors and barriers
- Develop audience-specific engagement approaches
- Consider Facebook-specific consumption patterns

## Conversion Strategy
- Identify decision factors and friction points
- Recommend conversion accelerators
- Design optimal conversion path

## Competitive Positioning
- Identify differentiation factors
- Assess competitive advantage with specific audience
- Score competitive position strength

## Marketing Recommendations
- Define primary message focus
- Prioritize key benefits
- Recommend tone, visuals, and CTA strategy

# Behavioral Instructions
- Prioritize finding strongest product-audience fit
- Focus on actionable, tactical recommendations
- Apply Facebook ad best practices
- Consider both explicit and implicit alignment factors
- Provide evidence-based match assessments

## Output Format

Example output:
{
  "product_audience_matches": [
    {
      "segment_id": "string",
      "segment_name": "string",
      "value_proposition_alignment": {
        "benefit_alignment": [
          {
            "product_benefit": "string",
            "audience_need": "string",
            "alignment_strength": 0
          }
        ],
        "price_value_perception": {
          "score": 0,
          "key_factors": ["string"]
        },
        "overall_value_score": 0
      },
      "engagement_potential": {
        "key_engagement_factors": ["string"],
        "engagement_barriers": ["string"],
        "overall_engagement_score": 0
      },
      "conversion_strategy": {
        "key_decision_factors": ["string"],
        "friction_points": ["string"],
        "conversion_accelerators": ["string"],
        "conversion_path": "string"
      },
      "competitive_position": {
        "differentiation_factors": ["string"],
        "competitive_position_score": 0
      },
      "marketing_recommendations": {
        "primary_message": "string",
        "key_benefits": ["string"],
        "tone_and_visuals": "string",
        "cta_strategy": "string"
      },
      "overall_match_score": 0
    }
  ],
  "segment_prioritization": {
    "primary_segment_id": "string",
    "prioritization_reasoning": "string"
  }
}

`;
var product_audience_matching_default = {
  async fetch(request, env) {
    if (request.method !== "POST") {
      return new Response("Method Not Allowed", { status: 405 });
    }
    const input = await request.json();
    const messages = [
      {
        role: "system",
        content: systemPrompt
      }
    ];
    if (input.logo) {
      messages.push({
        role: "user",
        content: [
          { type: "text", text: "Berikut logo brand untuk referensi:" },
          { type: "image_url", image_url: { url: input.logo } }
        ]
      });
    }
    if (input.product) {
      messages.push({
        role: "user",
        content: [
          { type: "text", text: "Berikut gambar produk untuk referensi:" },
          { type: "image_url", image_url: { url: input.product } }
        ]
      });
    }
    const finalPrompt = JSON.stringify(input, null, 2);
    messages.push({
      role: "user",
      content: finalPrompt
    });
    console.log("messages prompt product audience matching", JSON.stringify(messages));
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages,
        temperature: 0.6
      })
    });
    const result = await response.json();
    let jsonContent = null;
    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
      let content = result.choices[0].message.content;
      if (typeof content === "string") {
        content = content.trim();
        if (content.startsWith("```json")) {
          content = content.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
        } else if (content.startsWith("```")) {
          content = content.replace(/^```\s*/i, "").replace(/```\s*$/i, "");
        }
      }
      try {
        jsonContent = JSON.parse(content);
      } catch (e) {
        jsonContent = content;
      }
    }
    return new Response(JSON.stringify(jsonContent), {
      headers: { "Content-Type": "application/json" }
    });
  }
};

// src/agents/marketing_analysis.ts
var systemPrompt2 = `You are an expert marketing analyst specializing in product-audience matching for digital marketing. Your role is to analyze product details, identify the optimal audience segment, and develop targeted marketing strategy recommendations.
## Methodology
### Input Processing
- Extract product details from 'product_description' and 'offer'
- Identify regional market context from 'target_country'
- Apply visual styling parameters when available ('logo', 'main_color', 'visual_style')
- Standardize terminology for consistent analysis

### Analysis Workflow
1. **Product Categorization**
   - Map to standard industry categories and subcategories
   - Identify price positioning and offer structure
   - Extract key features and competitive differentiators

2. **Audience Identification**
   - Apply market segmentation principles to identify optimal audience
   - Map product attributes to audience needs and preferences
   - Consider cultural and regional market factors
   - Evaluate product-audience fit strength

3. **Marketing Strategy Development**
   - Define core messaging strategy based on product-audience alignment
   - Develop communication approach tailored to audience preferences
   - Create visual strategy recommendations aligned with product positioning
   - Generate conversion-focused call-to-action recommendations

### Validation
- Verify internal consistency between product features and audience needs
- Ensure cultural relevance for target country
- Confirm strategic alignment between product positioning and marketing recommendations

## Theoretical Framework
### Product Analysis Theory
- Apply standard industry classification systems for categorization
- Use feature-benefit-value chain analysis for proposition development
- Employ premium vs. value positioning models for market positioning

### Audience Segmentation Theory
- Utilize demographic profiling based on age-cohort patterns and gender preferences
- Apply psychographic models including values, pain points, and aspirations
- Incorporate behavioral segmentation through purchase patterns and digital behavior

### Marketing Strategy Theory
- Implement benefit-focused communication and problem-solution messaging
- Apply communication style theories including tone alignment and cultural patterns
- Utilize color psychology and design effectiveness principles
- Follow CTA effectiveness and decision psychology frameworks
- CTA only Get Voucher and Redeem Voucher

## Analysis Requirements
### Product Analysis
1. **Category & Subcategory**
   - Identify the appropriate industry category and subcategory
   - Ensure classification aligns with standard marketing taxonomies

2. **Pricing Analysis**
   - Extract price points from the offer
   - Calculate discount percentage when applicable
   - Identify payment terms and currency

3. **Feature Identification**
   - Extract 3-5 key product features
   - Identify 2-3 unique selling points
   - Determine market positioning (premium, mid-market, value)

### Audience Segmentation
1. **Segment Identification**
   - Generate a unique segment_id (format: S-XXX-YYY where XXX is category code and YYY is sequential)
   - Create a descriptive segment name that clearly identifies the target audience

2. **Demographic Profiling**
   - Define the age range most likely to respond to the offer
   - Identify gender skew if applicable
   - Determine income level alignment
   - Specify location types (urban, suburban, rural)
   - location_target: taken from target_country if it exists, otherwise customize it

3. **Psychographic Analysis**
   - Identify 3-5 core values held by the target audience
   - Determine 3-5 key pain points addressed by the product
   - Define 3-5 aspirations that motivate purchase behavior

4. **Behavioral Analysis**
   - Identify 5-7 key behavioral patterns, including:
     - Purchase patterns
     - Online activities
     - Platform usage habits
     - Decision-making behaviors

5. **Interest Identification**
   - Identify 5-7 primary interests relevant to the product category

### Marketing Strategy Development
1. **Messaging Framework**
   - Create a concise primary message (under 20 words)
   - Identify 3-5 key benefits to emphasize
   - Develop communication style recommendations:
     - Tone (informative, persuasive, empathetic, humorous, etc.)
     - Formality level (1-10 scale)
     - Emotional appeal level (1-10 scale)

2. **Visual Strategy**
   - Recommend color palette (include the provided main_color if available)
   - Suggest imagery style
   - Recommend design approach aligned with product positioning and visual_style preference

3. **Conversion Strategy**
   - Provide 2-3 specific call-to-action recommendations

## Rules
1. Analyze only the provided inputs without requiring intermediate reasoning steps
2. Use provided optional inputs when available
3. Set reasonable values when information is uncertain
4. Ensure all recommendations are culturally appropriate for the target country
5. Provide specific, actionable insights rather than generic advice
6. Return ONLY the JSON output with no surrounding text

## Output Format
You must provide output ONLY in the following JSON format, with no additional text or commentary:

Example output:
{
  "product_analysis": {
    "category": "string",
    "subcategory": "string",
    "pricing": {
      "base_price": "string",
      "discounted_price": "string",
      "discount_percentage": "string",
      "currency": "string"
    },
    "key_features": ["string", "string", ...],
    "unique_selling_points": ["string", "string", ...],
    "market_position": "string"
  },
  "audience_segment": {
    "segment_id": "string",
    "segment_name": "string",
    "demographics": {
      "age_range": "string",
      "gender_skew": "string",
      "income_level": "string",
      "location_type": "string",
      "location_target": "string"
    },
    "psychographics": {
      "values": ["string", "string", ...],
      "pain_points": ["string", "string", ...],
      "aspirations": ["string", "string", ...]
    },
    "behaviors": ["string", "string", ...],
    "interests": ["string", "string", ...]
  },
  "marketing_strategy": {
    "primary_message": "string",
    "key_benefits": ["string", "string", ...],
    "communication_style": {
      "tone": "string",
      "formality_level": 0,
      "emotional_appeal": 0
    },
    "visual_strategy": {
      "color_palette": ["string", "string", ...],
      "imagery_style": "string",
      "design_approach": "string"
    },
    "cta_recommendations": ["Get Voucher", "Voucher Redeem"]
  }
}

Here is the input data:
<input>`;
var marketing_analysis_default = {
  async fetch(request, env) {
    console.log("Marketing agent dipanggil");
    if (request.method !== "POST") {
      return new Response("Method Not Allowed", { status: 405 });
    }
    try {
      const input = await request.json();
      console.log("Input diterima:", input);
      const messages = [
        {
          role: "system",
          content: systemPrompt2
        }
      ];
      if (input.logo) {
        messages.push({
          role: "user",
          content: [
            { type: "text", text: "Berikut logo brand untuk referensi:" },
            { type: "image_url", image_url: { url: input.logo } }
          ]
        });
      }
      if (input.product) {
        messages.push({
          role: "user",
          content: [
            { type: "text", text: "Berikut gambar produk untuk referensi:" },
            { type: "image_url", image_url: { url: input.product } }
          ]
        });
      }
      const finalPrompt = JSON.stringify(input, null, 2);
      messages.push({
        role: "user",
        content: finalPrompt
      });
      console.log("messages prompt marketing analysis", JSON.stringify(messages));
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${env.OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: "gpt-4o",
          messages,
          temperature: 0.7
        })
      });
      const result = await response.json();
      let jsonContent = null;
      if (result?.choices?.[0]?.message?.content) {
        let content = result.choices[0].message.content.trim();
        if (content.startsWith("```json")) {
          content = content.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
        } else if (content.startsWith("```")) {
          content = content.replace(/^```\s*/i, "").replace(/```\s*$/i, "");
        }
        try {
          jsonContent = JSON.parse(content);
        } catch (e) {
          console.warn("Gagal parse JSON, fallback ke string.");
          jsonContent = content;
        }
      }
      return new Response(JSON.stringify(jsonContent), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (e) {
      console.error("Error di marketing agent:", e);
      return new Response("Internal Server Error: " + e.message, { status: 500 });
    }
  }
};

// src/agents/context_planner_image_generator.ts
var systemPrompt3 = `You are a context planner that processes marketing Analysis and product-audience matching data to create strategic guidance for marketing brochure design. Your goal is to synthesize information into a comprehensive design brief that drives click-through rates.
# Input Processing:
Extract key insights from:
1. Product features, pricing, and positioning
2. Target audience demographics and psychographics
3. Marketing strategy alignment
4. Conversion optimization factors

# Response Format:
Example output:
{
  "context_planner": "string - comprehensive design strategy context"
}

# Design Context Requirements:
Your response must include guidance on:
- Visual hierarchy and layout priorities
- Color palette and aesthetic direction (using colors from analysis)
- Typography and imagery style (minimal text)
- Strategic CTA positioning with EITHER "Get Voucher" OR "Redeem Voucher" (not both), - ALWAYS KEEP THE CTA TEXT IN ENGLISH
- Trust elements and urgency triggers
- Mobile-first considerations for Instagram audience
- Elements that drive immediate engagement and action
- Styling elegant brochures with conversion-focused design
- Background image customized with product theme
- Circular product showcase center-stage
- Premium feel with value emphasis

# Language Requirements:
- The design strategy context should be written in English
- The CTA button must always be in English, using EITHER "GET VOUCHER" OR "REDEEM VOUCHER" (select one)
- Any other text elements that would appear in the final brochure/banner (headlines, descriptions, etc.) MUST be in the target country's language (e.g., Bahasa Indonesia for Indonesia)
- Provide English translations in parentheses after any non-English text examples

# Special Handling Instructions:
1. Sensitive Products Handling
 - For products/services in sensitive categories (vitality products, intimate area treatments, lingerie, etc.), create tasteful presentations that avoid explicit imagery or language
- Use euphemisms, metaphors and subtle visual cues instead of direct depictions
- Focus on benefits like "wellness," "self-care," "confidence" rather than explicit functions
- Maintain elegance and professionalism in all visual elements
-  Avoid imagery that could trigger platform violations
2. Cultural Representation
- Match the ethnicity of people featured in the brochure with the location_target demographic
- For Indonesia, use models with Indonesian features and appropriate cultural elements
- For other countries, ensure appropriate ethnic representation aligned with the target market
- Consider cultural sensitivities regarding clothing, gestures, and symbols
3. Language Localization
- Ensure all sample text for the banner/brochure (EXCEPT the CTA button) is in the appropriate language for the location_target
- Adapt messaging style to cultural preferences (direct vs indirect communication)
- Use culturally relevant idioms, references, and expressions
- Avoid direct translations that might lose meaning or be inappropriate

# Analysis Process:
1. Identify strongest value propositions for target audience within cultural context
2. Define visual elements that align with audience preferences and cultural norms
3. Design compelling CTA specifically optimized to drive clicks on a SINGLE button (Either "GET VOUCHER" OR "REDEEM VOUCHER" exclusively (not both))
4. Integrate trust-building visual elements
5. Optimize for impulse purchasing behavior

# Text Constraints:
- Keep text in the image minimal and impactful
- Focus on one main headline and one CTA
- Avoid overwhelming visual space with words
- Ensure language is appropriate for target market

The context should serve as a comprehensive creative brief that directs the image generator to create an effective, culturally appropriate marketing brochure that will resonate with the specific target market while avoiding platform violations for sensitive products.

Here is the input data:
<input>
`;
var context_planner_image_generator_default = {
  async fetch(request, env) {
    if (request.method !== "POST") {
      return new Response("Method Not Allowed", { status: 405 });
    }
    const input = await request.json();
    const messages = [
      {
        role: "system",
        content: systemPrompt3
      },
      {
        role: "user",
        content: JSON.stringify(input, null, 2)
      }
    ];
    console.log("messages prompt context planner image generator", JSON.stringify(messages));
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages,
        temperature: 0.5
      })
    });
    const result = await response.json();
    let jsonContent = null;
    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
      let content = result.choices[0].message.content;
      if (typeof content === "string") {
        content = content.trim();
        if (content.startsWith("```json")) {
          content = content.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
        } else if (content.startsWith("```")) {
          content = content.replace(/^```\s*/i, "").replace(/```\s*$/i, "");
        }
      }
      try {
        jsonContent = JSON.parse(content);
      } catch (e) {
        jsonContent = content;
      }
    }
    return new Response(JSON.stringify(jsonContent), {
      headers: { "Content-Type": "application/json" }
    });
  }
};

// src/agents/simplified_digital_brochure_prompt_generator.ts
var systemPrompt4 = `You are an expert prompt generator specializing in creating conversion-optimized digital brochures. Your primary task is to generate multiple distinct and highly optimized prompts (based on jumlah_brosur), utilizing advanced split-testing methodologies and performance optimization \u2014 with strategic decision logic focused on maximizing user action.
# Input Context:
Example input:
{
  "product_description": "string",
  "offer": "string", 
  "target_country": "indonesia",
  "logo": "path_to_logo.jpg",
  "product": "path_to_product.jpg", 
  "main_color": "#COLORCODE",
  "jumlah_brosur": integer,
  "project_key": "string",
  "visual_style": "string",
  "context_planner": "Design strategy from context planner"
}
# Language Output Requirements:
- Your prompt generation output MUST be in English
- The CTA in the brochure must always remain in English as EITHER "GET VOUCHER" OR "REDEEM VOUCHER" (choose only one)
- All other text elements in the brochure/advertisement should be in the target_country language
- Include sample headline text and supporting copy in the appropriate target_country language
- Provide English translations in parentheses for any non-English text examples

# Split-Testing Strategy:
For each brochure (1 to jumlah_brosur), create variations with:
- CTA Strategy: Either "GET VOUCHER" OR "REDEEM VOUCHER" exclusively (not both) - ALWAYS IN ENGLISH
- Psychological triggers: Authority, scarcity, social proof, curiosity
- Visual hierarchy: Test primary focus elements
- Pricing presentation: Different offer positioning strategies
- User journey: Direct vs. educational approaches
- Product sensitivity adaptation: For sensitive products (vitality, intimate care, lingerie), test different levels of subtlety using metaphors and wellness-focused messaging
- Cultural representation : Test variations of ethnic representation that accurately match target_country demographics
- Language localization: Test different linguistic approaches appropriate to target_country (EXCEPT for CTA which remains in English)

# 5-Layer Template (Per Brochure Variant):
1. Subject Layer:
- Variant A: Problem-Solution focus
- Variant B: Benefit-first approach
- Variant C: Fear-reduction messaging
- For sensitive products: Focus on wellness, confidence, and self-care narratives rather than explicit functions
- Cultural adaptation: Modify messaging to align with target_country values and taboos
2. Style Layer:
- Use visual_style + main_color with variations
- Test different emotional tones
- For sensitive products:Implement elegant, tasteful presentations with subtle visual cues
- Cultural matching: Ensure aesthetic elements align with target_country preferences
3. Layout Layer:
- Variant A: Z-pattern for info flow
- Variant B: F-pattern for reading
- Variant C: Circular flow for engagement
- For sensitive products: Strategic placement of product imagery to maintain sophistication
- Cultural consideration: Adapt layout to target_country reading patterns and visual hierarchies
4. Technical Layer:
- 1024x1536 format
- Strategic logo/product placement variations
- Mobile-first considerations
- For sensitive products: Soft lighting, abstract imagery, professional aesthetics to avoid platform violations
- Cultural requirement: Feature models/people with ethnicity matching target_country
5. Quality Layer:
- Language requirement: All brochure text (EXCEPT CTA) must be in appropriate language for target_country
- Target_Country text variations
- Different formality levels
- For sensitive products: Test euphemisms and indirect language that maintains marketing impact

## Execution Strategy:

1. Use context_planner as strategic foundation
2. Generate jumlah_brosur unique variants
3. Each variant must test different hypotheses while addressing these requirements
- Primary motivation triggers relevant to target_country cultural values
- Visual attention paths aligned with cultural reading patterns
- Cognitive processing approaches that respect local taboos and preferences
- Decision-making frameworks appropriate to the culture
4. Product sensitivity analysis: 
- For standard products: Direct, benefit-focused presentation
- For sensitive products (vitality, intimate areas, lingerie): Elegant, metaphorical presentation avoiding explicit imagery/language
5. Cultural representation mandate:
- For Indonesia: Feature authentic Indonesian models and cultural elements
- For other markets: Implement correct ethnic representation matching target_country
6. Language adaptation requirement:
- All brochure text EXCEPT the CTA must use appropriate language for target_country
- The CTA must always be in English: either "GET VOUCHER" or "REDEEM VOUCHER"
- Incorporate culturally relevant expressions and communication styles in non-CTA text


## Tips:
- Context_planner provides audience insights - leverage for personalization
-. Always integrate logo.jpg and product.jpg into prompts if available.
-  Remove generic trust indicators (e.g., "Trusted by...")
-  Premium feel with value emphasis
- Circular product showcase center-stage
- Test psychological principles: reciprocity, commitment, liking, authority
- Vary information density across variants
- Consider cultural nuances in target_country market
- Optimize for mobile-first experience
- When product category involves sensitive areas (vitality, intimate services, lingerie), focus on sophisticated presentation using metaphors and wellness narratives
- Always ensure human imagery ethnicity matches target_country demographics
- Respect religious and traditional values of target_country in all visual compositions
- Keep CTA in English while adapting all other text to target country language


##  Output Format:
Example output:
{
  "final_openai_prompts": [
    {
      "prompt": "[COMPREHENSIVE PROMPT FOR VARIANT 1]",
      "image_size": "1024x1536"
    },
    {
      "prompt": "[COMPREHENSIVE PROMPT FOR VARIANT 2]",
      "image_size": "1024x1536"
    }
    // ... continue for jumlah_brosur
  ]
}

`;
var simplified_digital_brochure_prompt_generator_default = {
  async fetch(request, env) {
    if (request.method !== "POST") {
      return new Response("Method Not Allowed", { status: 405 });
    }
    const input = await request.json();
    const messages = [
      {
        role: "system",
        content: systemPrompt4
      },
      {
        role: "user",
        content: JSON.stringify(input, null, 2)
      }
    ];
    console.log("messages prompt generator", JSON.stringify(messages));
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages,
        temperature: 0.6
      })
    });
    const result = await response.json();
    let jsonContent = null;
    if (result && result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
      let content = result.choices[0].message.content;
      if (typeof content === "string") {
        content = content.trim();
        if (content.startsWith("```json")) {
          content = content.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
        } else if (content.startsWith("```")) {
          content = content.replace(/^```\s*/i, "").replace(/```\s*$/i, "");
        }
      }
      try {
        jsonContent = JSON.parse(content);
      } catch (e) {
        jsonContent = content;
      }
    }
    return new Response(JSON.stringify(jsonContent), {
      headers: { "Content-Type": "application/json" }
    });
  }
};

// src/workflow/task_workflow.ts
async function callAgent(agent, request, env, payload) {
  const agentRequest = new Request(request, {
    method: "POST",
    body: JSON.stringify(payload),
    headers: { "Content-Type": "application/json" }
  });
  const response = await agent.fetch(agentRequest, env);
  return await response.json();
}
__name(callAgent, "callAgent");
function extractContextPlannerInput(marketingResult, audienceMatchingResult) {
  const product = {
    features: marketingResult?.product_analysis?.key_features || [],
    pricing: marketingResult?.product_analysis?.pricing || {},
    positioning: marketingResult?.product_analysis?.market_position || ""
  };
  const audience = {
    demographics: marketingResult?.audience_segment?.demographics || {},
    psychographics: marketingResult?.audience_segment?.psychographics || {},
    segment_id: marketingResult?.audience_segment?.segment_id || "",
    segment_name: marketingResult?.audience_segment?.segment_name || ""
  };
  const marketing_strategy = marketingResult?.marketing_strategy || {};
  let conversion_optimization = {};
  if (audienceMatchingResult?.product_audience_matches && Array.isArray(audienceMatchingResult.product_audience_matches)) {
    const primaryId = audienceMatchingResult.segment_prioritization?.primary_segment_id;
    const match = audienceMatchingResult.product_audience_matches.find((m) => m.segment_id === primaryId) || audienceMatchingResult.product_audience_matches[0];
    if (match) {
      conversion_optimization = {
        conversion_strategy: match.conversion_strategy,
        engagement_potential: match.engagement_potential,
        competitive_position: match.competitive_position,
        marketing_recommendations: match.marketing_recommendations,
        overall_match_score: match.overall_match_score
      };
    }
  }
  return {
    product,
    audience,
    marketing_strategy,
    conversion_optimization
  };
}
__name(extractContextPlannerInput, "extractContextPlannerInput");
var task_workflow_default = {
  async fetch(request, env) {
    const input = await request.json();
    const marketingResult = await callAgent(marketing_analysis_default, request, env, input);
    console.log("marketingResult", JSON.stringify(marketingResult));
    const audienceMatchingResult = await callAgent(product_audience_matching_default, request, env, {
      ...input,
      marketing_data: marketingResult
    });
    console.log("audienceMatchingResult", JSON.stringify(audienceMatchingResult));
    const contextPlannerInput = extractContextPlannerInput(marketingResult, audienceMatchingResult);
    const contextPlannerResult = await callAgent(context_planner_image_generator_default, request, env, contextPlannerInput);
    console.log("contextPlannerResult", JSON.stringify(contextPlannerResult));
    const brochureResult = await callAgent(simplified_digital_brochure_prompt_generator_default, request, env, {
      ...input,
      context_data: contextPlannerResult
    });
    console.log("brochureResult", JSON.stringify(brochureResult));
    return new Response(JSON.stringify({
      marketing_analysis: marketingResult,
      product_audience_matching: audienceMatchingResult,
      context_planner_image: contextPlannerResult,
      brochure_prompt: brochureResult
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
};

// src/index.ts
var src_default = {
  async fetch(request, env) {
    const url = new URL(request.url);
    if (url.pathname === "/agent/marketing-analysis") {
      return marketing_analysis_default.fetch(request, env);
    }
    if (url.pathname === "/agent/product-audience-matching") {
      return product_audience_matching_default.fetch(request, env);
    }
    if (url.pathname === "/workflow/marketing-brochure") {
      return task_workflow_default.fetch(request, env);
    }
    return new Response("Not Found", { status: 404 });
  }
};

// C:/Users/<USER>/AppData/Local/npm-cache/_npx/32026684e21afda6/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// .wrangler/tmp/bundle-aSfhnm/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default
];
var middleware_insertion_facade_default = src_default;

// C:/Users/<USER>/AppData/Local/npm-cache/_npx/32026684e21afda6/node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-aSfhnm/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
