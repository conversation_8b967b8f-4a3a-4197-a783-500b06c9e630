## [Unreleased]
### Fixed
- Fixed logger queue payload structure in `logToR2` (`producer/utils/r2_logger.ts`): now wraps log data in a `body` property as expected by the logger worker. This prevents 'Log queue message missing body' errors in `logger/log/worker.ts`.

### Added
- Implemented auto-retry and resume feature in `runWorkflow` (`producer/workflow/task_workflow.ts`). If a workflow fails at any agent step, the error now includes the last successful agent step (`last_success_agent`). The consumer/queue can re-queue the task with this info, and the workflow will resume from the last successful step, skipping previous steps. This reduces wasted computation and cost on partial failures and improves reliability for long-running workflows.
- Implemented brochure image generation, upload, and storage workflow for nail art service marketing.
- Added three new handlers in `consumer/handlers/`:
  - `generate_brochure_image.ts`: Generates images from prompts using GPT-Image-1 API.
  - `upload_brochure_image.ts`: Uploads images to Cloudflare R2 and returns public URLs.
  - `store_brochure_image_urls.ts`: Stores uploaded image URLs and metadata in Cloudflare KV.
- Updated `consumer/queues/workflow_consumer.ts` to trigger this process after task completion if `brochure_prompt.final_openai_prompts` is present, with a limit of 10 prompts per task. Errors and results are logged in KV.
- Now each agent step in the `runWorkflow` function of `producer/workflow/task_workflow.ts` records its token usage separately by calling `setAgentUsage` after each `callAgentQueue`.
- Added a guard clause in `generateImagesFromBrochures` (`producer/handlers/generate_images_from_brochures.ts`) to return an error if prompts is not an array or is empty, preventing TypeError on image generation.
- Planned: Added implementation plan for CTA Button Mapping Agent (`implemtasi/cta-button-mapping.md`) to process output from `generateImagesFromBrochures`, detect CTA buttons, enlarge area, and generate HTML mapping strings. All implementation will be under `implemtasi/cta_agent/`.
- Implemented CTA Button Mapping Agent in `producer/agents/cta_button_mapping.ts` to process image results and generate HTML image maps for CTA buttons, following the specification in `system_prompt/cta_button_mapping.txt`.
- Enhanced `generateImagesFromBrochures` (`producer/handlers/generate_images_from_brochures.ts`) to include `usage_cta` (CTA Button Mapping agent usage) and `cta_html` (HTML image map) fields in each image result object, enabling downstream access to CTA mapping data and usage per image.
- Setelah status task selesai (completed) diupdate di KV pada `consumer/queues/workflow_consumer.ts`, sistem sekarang juga mengirim status, task_id, dan project_key ke webhook eksternal `https://ai.gass.co.id/webhook.html` menggunakan HTTP POST dengan payload JSON. Jika terjadi error saat pengiriman webhook, error dicatat di log.
- Loadtime pada status task sekarang disimpan dan dikirim dalam satuan menit (float, dua desimal) pada field status:completed dan payload webhook, bukan lagi milidetik.
- Updated `producer/handlers/generate_images_banner.ts` to accept an object with `square_prompts` and `portrait_prompts` arrays as input.
- The handler now validates that at least one of these arrays is present and non-empty before generating images.
- Generates images for all prompts in both `square_prompts` and `portrait_prompts` if present.
- Each result object now includes a `type` field indicating whether the image is from a square or portrait prompt.
- Returns an error object if neither array is present or both are empty.
- All workflow input and result objects are now logged as JSON files to a Cloudflare R2 bucket using the `logToR2` utility in `producer/utils/r2_logger.ts`. The files are named as `logs/{task_id}_input.json` and `logs/{task_id}_result.json` for each task, and are accessible via public URLs. This logging is performed at the start and end of each workflow execution (see `runWorkflow` and `runWorkflow_banner` in `producer/workflow/task_workflow.ts`).
- Log file input dan result kini menyertakan field tambahan untuk keperluan RAG: task_id, logType, timestamp, content, metadata (dataType, keys). Struktur ini membuat log siap dipakai untuk pipeline Retrieval-Augmented Generation (RAG) downstream.
- Implemented vectorization/embeddings pipeline: setiap hasil agent pada workflow `runWorkflow_banner` di `producer/workflow/task_workflow.ts` kini di-vectorize menggunakan OpenAI embeddings (text-embedding-ada-002) melalui utility baru `generateEmbedding` di `producer/agents/agent.ts`. Embedding dan data dicatat ke R2 untuk keperluan RAG pipeline downstream.
- Implemented agent result caching in `producer/workflow/task_workflow.ts` for the `runWorkflow_banner` function. Results for `audience_analysis`, `audience_profiling`, and `product_audience_matching` are now cached using a hash of the input. If the same input is received, the workflow retrieves results from cache and skips directly to `ads-copy-generation`. The cache uses `getAgentResultCache` and `setAgentResultCache` from `producer/utils/token_usage.ts` and stores results in KV with a 1-hour TTL. This reduces redundant agent calls and improves workflow performance.
- Implemented `/update-system-prompt` POST API endpoint in `producer/index.ts` to allow updating system prompts in `env.PROMPT` via x-www-form-urlencoded input (`agent` as key, `prompt` as value). Returns JSON status or error.
- Implemented `/get-system-prompt` GET API endpoint in `producer/index.ts` to allow retrieving system prompts from `env.PROMPT` via the `agent` query parameter. Returns JSON with the prompt or error if not found or missing parameter.
- Implemented `/check-status-sse` API endpoint in `producer/index.ts` for real-time task status streaming using Server-Sent Events (SSE). The handler is implemented as `stream` in `producer/handlers/check_status.ts` and streams status updates every second until the task is completed, failed, or times out (15 minutes/900,000 ms). This enables clients to receive live progress updates without polling.
- Workflow log (input/result) now sent to LOGGER_QUEUE instead of direct R2 write. Logger worker (`logger/log/worker.ts`) consumes the queue and writes logs to R2 asynchronously. `logToR2` refactored to only send to queue; R2 write logic moved to the worker.
- Workflow response (runWorkflow & runWorkflow_banner) now includes log_links field, listing all agent input/output log URLs for audit and traceability.
- Updated `producer/handlers/generate_images_banner.ts` to support multimodal OpenAI image generation: if both `logo` and `product_image` are provided, the handler encodes them as base64 and sends as `input_image` (with prompt as `input_text`) to OpenAI's responses.create API. Falls back to legacy Cloudflare Gateway fetch if not. Fixed linter errors for input_image structure (image_url as string, not object).
- Added `/test-vertex-image` API endpoint in `producer/index.ts` for testing Google Vertex AI image generation via Cloudflare AI Gateway. Accepts POST with `{ prompt: string }`, returns base64 image from Vertex Imagen 3.0.
- Implemented `/add-system-prompt` POST API endpoint in `producer/index.ts` to allow adding (or overwriting) system prompts in `env.PROMPT` via x-www-form-urlencoded input (`agent` as key, `prompt` as value). Returns JSON status or error.
- Implemented `/list-system-prompts` GET API endpoint in `producer/index.ts` to return all system prompt keys (agent names) in `env.PROMPT` as a JSON array. Only GET allowed, returns 405 otherwise.
- Added POST API endpoint `/run-auto-workflow` in `producer/index.ts` to execute a dynamic, multi-step agent workflow based on a JSON array of steps and input. Calls `taskWorkflow.runAutoWorkflow` and returns all step results. Enables flexible, declarative workflow execution for rapid prototyping and chaining agents.
- The response of `/run-auto-workflow` now includes `results` (array of step results), `loadtime_summary` (per step), and `cost_summary` (total usage/cost), not just the array of step results.
- Now supports advanced input mapping: each step can use `input_from_step`, `input_template`, or `input_keys` to take input from any previous step, not just the last one. This enables complex, multi-source, and template-based input chaining between agents.
- Added `producer/agents/agent_image.ts`, a flexible image agent that automatically chooses between brochure/banner logic based on input structure. Can be used for steps like `brochure_image_generator` or `generate_images_banner` in workflow JSON.

### Changed
- Embedded the full marketing analysis framework prompt from `