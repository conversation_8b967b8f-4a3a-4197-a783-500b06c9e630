export default {
    async queue(batch: any, env: any) {
    if (!batch || !batch.messages || !Array.isArray(batch.messages)) {
      console.error('Queue batch missing messages array:', JSON.stringify(batch));
      return;
    }
    for (const message of batch.messages) {
      if (!message || !message.body) {
        console.error('Queue message missing body:', JSON.stringify(message));
        await env.TASK_STATUS.put('status:unknown', JSON.stringify({ status: 'failed', error: 'Message body is undefined' }));
        continue;
      }
      const { task_id, logType, data } = message.body;
      if (!task_id || !logType) {
        console.error('Log queue message missing task_id or logType:', message.body);
        continue;
      }
      const fileName = `logs/${task_id}_${logType}.json`;
      const expireAt = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(); // 60 hari
      const ragLog = {
        task_id,
        logType,
        timestamp: new Date().toISOString(),
        content: data,
        metadata: {
          dataType: typeof data,
          keys: data && typeof data === 'object' ? Object.keys(data) : undefined
        }
      };
      const jsonData = JSON.stringify(ragLog, null, 2);
      await env.BUCKET_AI.put(fileName, jsonData, {
        httpMetadata: { contentType: "application/json" },
        customMetadata: { expire_at: expireAt }
      });
      // Optionally, log the public URL
      const publicUrl = `https://r2-agent-ai.gass.co.id/${fileName}`;
      console.log('Log written to R2:', publicUrl);
    }
  }
}
