# Project Documentation: agent-cloudflare

## Recent Change: Dynamic Auto Workflow API Endpoint

- Added a new POST API endpoint `/run-auto-workflow` in `producer/index.ts`.
- This endpoint allows clients to execute a dynamic, multi-step agent workflow by providing an array of step definitions (with system_prompt, model, status_worker, disable_log, etc) and an initial input object.
- **Now supports advanced input mapping per step:**
  - `input_from_step`: ambil field tertentu dari step manapun, contoh:
    ```json
    { "input_from_step": { "fieldA": "step1.data.ocrText", "fieldB": "step2.data.summary" } }
    ```
  - `input_template`: gunakan template string untuk menggabungkan/mapping field dari step manapun, contoh:
    ```json
    { "input_template": { "text": "${step1.data.ocrText} - ${step2.data.summary}" } }
    ```
  - `input_keys`: array mapping field dari step manapun, contoh:
    ```json
    { "input_keys": [ { "from": "step1", "field": "ocrText", "as": "ocr" }, { "from": "step2", "field": "summary" } ] }
    ```
- The endpoint calls `taskWorkflow.runAutoWorkflow`, which executes each step in order, passing the result of each step as input to the next (or as mapped).
- **Response now includes:**
  - `results`: array of step results, each with step name and result object
  - `loadtime_summary`: object with loadtime per step (if available)
  - `cost_summary`: total usage and cost summary for the workflow
- Example request body:
  ```json
  {
    "steps": [
      { "system_prompt": "website_screenshot_ocr" },
      { "system_prompt": "prompt_audience_analysis", "input_from_step": { "text": "website_screenshot_ocr.data.ocrText" } },
      { "system_prompt": "step3", "input_template": { "summary": "${website_screenshot_ocr.data.ocrText} - ${prompt_audience_analysis.data.result}" } }
    ],
    "input": { ... },
    "task_id": "your-task-id"
  }
  ```
- Example response:
  ```json
  {
    "results": [ { "step": "marketing_analysis", "result": { ... } }, ... ],
    "loadtime_summary": { "marketing_analysis": 1.23, ... },
    "cost_summary": { ... }
  }
  ```
- Use case: enables flexible, declarative workflow execution for rapid prototyping, chaining agents, or custom pipelines without code changes.

## Rationale
- Enables dynamic, client-driven workflow execution for agent pipelines.
- Reduces need for hardcoded workflow logic and supports rapid experimentation.

## Affected Files
- `producer/index.ts`: Adds the new endpoint and handler logic.
- `producer/workflow/task_workflow.ts`: Exports `runAutoWorkflow` for use by the endpoint.

## Recent Change: Brochure Image Generation, Upload, and Storage Workflow

- Implemented a new workflow for generating, uploading, and storing digital brochure images for nail art services, triggered after task completion.
- Added three new modular handlers in `consumer/handlers/`:
  - `generate_brochure_image.ts`: Calls GPT-Image-1 API to generate images from prompts.
  - `upload_brochure_image.ts`: Uploads generated images to a Cloudflare R2 bucket and returns public URLs.
  - `store_brochure_image_urls.ts`: Stores the uploaded image URLs and metadata in Cloudflare KV.
- Updated `consumer/queues/workflow_consumer.ts` to trigger this process after marking a task as completed, if `brochure_prompt.final_openai_prompts` is present in the result. The process is limited to 10 prompts per task for performance and reliability.
- Errors in any step are logged to KV, and the final status and image URLs are stored in KV as well.

## Rationale
- Automates the end-to-end process of generating marketing brochure images, uploading them, and tracking their URLs for downstream use.
- Modular handler design improves maintainability, testability, and separation of concerns.
- Error handling and status updates in KV ensure robust observability and traceability.

## Affected Files
- `consumer/handlers/generate_brochure_image.ts`: New handler for image generation.
- `consumer/handlers/upload_brochure_image.ts`: New handler for image upload to R2.
- `consumer/handlers/store_brochure_image_urls.ts`: New handler for storing URLs in KV.
- `consumer/queues/workflow_consumer.ts`: Updated to trigger the new workflow after task completion.

## Recent Change: Context Planner Input Extraction (Key Insights Only)

- The workflow in `src/workflow/task_workflow.ts` now uses the `extractContextPlannerInput` helper to synthesize and extract only the most relevant key insights from both the marketing analysis and product-audience matching results.
- The input to the context planner agent is a structured object containing:
  1. Product features, pricing, and positioning (from marketing analysis)
  2. Target audience demographics and psychographics (from marketing analysis)
  3. Marketing strategy alignment (from marketing analysis)
  4. Conversion optimization factors (from product-audience matching)
- This ensures the context planner receives only actionable, high-value insights for downstream brochure/image generation.

## Rationale
- Focusing the context planner input on key insights improves the quality and relevance of generated outputs.
- The helper function centralizes and standardizes the extraction logic, making the workflow more maintainable and auditable.

## Affected Files
- `src/workflow/task_workflow.ts`: Uses `extractContextPlannerInput` to build context planner input from marketing and audience matching results.

## Recent Change: Marketing Analysis Framework Integration

- The full contents of `system_prompt/prompt_marketing_analysis_framework.txt` have been moved into the `systemPrompt` variable in `src/marketing-analis.ts`.
- The systemPrompt variable now contains the entire marketing analysis framework as a template literal, preserving the `<input>` placeholder for dynamic data injection.
- This change centralizes the marketing analysis logic and ensures the prompt is always up to date with the latest framework.

## Rationale
- Embedding the prompt directly in the code improves maintainability and reduces the risk of desynchronization between the code and the prompt file.
- The prompt is now easier to update and review as part of the main codebase.

## Affected Files
- `src/marketing-analis.ts`: systemPrompt variable updated.
- `system_prompt/prompt_marketing_analysis_framework.txt`: content now referenced in code, but file remains for reference.

## Next Steps
- Update `system_prompt/prompt_marketing_analysis_framework.txt` if the framework changes, and manually sync to `src/marketing-analis.ts` as needed.

## Recent Change: Digital Brochure Prompt Generator Input Update

- The input field for context planner strategy in `src/agents/simplified_digital_brochure_prompt_generator.ts` has been renamed from `context_planner` to `context_data`.
- All instructions, prompt templates, and references now consistently use `context_data` as the key for passing design strategy and audience insights from the context planner agent.
- This change ensures clarity and consistency across the workflow and agent interfaces.

## Affected Files
- `src/agents/simplified_digital_brochure_prompt_generator.ts`: Updated all references from `context_planner` to `context_data` in the system prompt and input structure.

## Recent Change: Product Audience Matching Prompt Format Update

- The systemPrompt in `src/agents/product_audience_matching.ts` no longer uses triple backtick markdown (```json ... ```) for the Output Format block. The JSON example is now presented as plain indented text within the template literal.
- This change prevents potential syntax errors in TypeScript/JavaScript and ensures prompt consistency across agents.

## Rationale
- Removing markdown code block formatting from the prompt avoids TypeScript parsing errors and improves maintainability.

## Affected Files
- `src/agents/product_audience_matching.ts`: Updated Output Format in systemPrompt to use plain JSON, not markdown code block.

## Recent Change: Context Planner Image Generator Prompt Format Update

- The systemPrompt in `src/agents/context_planner_image_generator.ts` no longer uses triple backtick markdown (```json ... ```) for the Response Format block. The JSON example is now presented as plain indented text within the template literal.
- This change prevents potential syntax errors in TypeScript/JavaScript and ensures prompt consistency across agents.

## Affected Files
- `src/agents/context_planner_image_generator.ts`: Updated Response Format in systemPrompt to use plain JSON, not markdown code block.

## Recent Change: Marketing Analysis Prompt Format Update

- The systemPrompt in `src/agents/marketing_analysis.ts` no longer uses triple backtick markdown (```json ... ```) for the Output Format block. The JSON example is now presented as plain indented text within the template literal.
- This change prevents potential syntax errors in TypeScript/JavaScript and ensures prompt consistency across agents.

## Rationale
- Removing markdown code block formatting from the prompt avoids TypeScript parsing errors and improves maintainability.

## Affected Files
- `src/agents/marketing_analysis.ts`: Updated Output Format in systemPrompt to use plain JSON, not markdown code block.

## Recent Change: Queue Consumer KV Binding Correction and Error Handling

- The queue consumer in `consumer/queues/workflow_consumer.ts` now uses `env.TASK_STATUS` (as defined in wrangler.toml) for all KV operations instead of the previously used `env.WORKFLOW_QUEUE`.
- A guard clause checks for the presence of `env.TASK_STATUS` and logs an error if it is missing, preventing runtime exceptions.
- The handler still checks for `message.body` and logs an error and writes a failed status if it is undefined.
- These changes ensure the worker uses the correct KV binding and is robust against missing bindings or malformed messages.

## Rationale
- Ensures the worker uses the correct, configured KV binding for status/result storage.
- Prevents runtime errors due to missing bindings or malformed messages, improving reliability and observability.

## Affected Files
- `consumer/queues/workflow_consumer.ts`: Switched to `env.TASK_STATUS` and added guard for its presence.

## Recent Change: Agent Module Import Error Investigation

- Investigated linter errors in `producer/workflow/task_workflow.ts` regarding missing modules for agent imports (`marketing_analysis`, `product_audience_matching`, `context_planner_image_generator`, `simplified_digital_brochure_prompt_generator`).
- Verified that all required agent modules exist in `producer/agents/`, are correctly named, and each exports a default object as required by the workflow.
- Confirmed that the linter errors are likely due to a tooling or environment issue, not missing or misnamed files.
- No code changes were necessary; the project structure and exports are correct.

## Rationale
- Ensures that the workflow can reliably import and use all agent modules as designed.
- Confirms that the project structure and module exports are consistent with project standards and requirements.
- Documents the investigation for future reference in case similar linter or tooling errors occur.

## Affected Files
- `producer/workflow/task_workflow.ts`: Linter errors investigated, no code changes required.
- `producer/agents/marketing_analysis.ts`: Confirmed default export exists.
- `producer/agents/product_audience_matching.ts`: Confirmed default export exists.
- `producer/agents/context_planner_image_generator.ts`: Confirmed default export exists.
- `producer/agents/simplified_digital_brochure_prompt_generator.ts`: Confirmed default export exists.

## Recent Change: Improved Error Handling for Brochure Image Generation (OpenAI API)

- Updated `producer/handlers/generate_images_from_brochures.ts` to robustly handle non-JSON responses from the OpenAI image generation API, such as Cloudflare rate limit errors (e.g., error code 1015) or HTML/text error pages.
- The handler now checks the response content type before attempting to parse as JSON. If the response is not JSON, it captures and returns the raw error message for better diagnostics and observability.
- This prevents misleading JSON parsing errors and ensures that upstream consumers receive accurate error information.

## Rationale
- The OpenAI API may return non-JSON error responses (such as HTML or plain text) when rate limited or when other errors occur. Attempting to parse these as JSON caused confusing error messages and made debugging difficult.
- By detecting and handling non-JSON responses, the handler now provides clearer error reporting and improves the reliability of the image generation workflow.

## Affected Files
- `producer/handlers/generate_images_from_brochures.ts`: Enhanced error handling for non-JSON API responses.

## Recent Change: Agent Prompt Format Update (Markdown Removal)

- The systemPrompt in `consumer/agents/marketing_analysis.ts` and `consumer/agents/context_planner_image_generator.ts` no longer use triple backtick markdown (```json ... ```) for the Output/Response Format block. The JSON example is now presented as plain indented text within the template literal.
- This change prevents potential syntax errors in TypeScript/JavaScript and ensures prompt consistency across agents.

## Rationale
- Removing markdown code block formatting from the prompt avoids TypeScript parsing errors and improves maintainability.

## Affected Files
- `consumer/agents/marketing_analysis.ts`: Updated Output Format in systemPrompt to use plain JSON, not markdown code block.
- `consumer/agents/context_planner_image_generator.ts`: Updated Response Format in systemPrompt to use plain JSON, not markdown code block.

## Recent Change: Type Safety for Image Token Usage Table

- Added explicit TypeScript types for image size and quality in `producer/utils/image_token_usage.ts`.
- Updated the `getImageTokenUsage` function to use type assertions, ensuring type safety and resolving TypeScript errors related to implicit 'any' when indexing the usage table.

## Rationale
- Prevents TypeScript errors and ensures that only valid image sizes and quality levels are used when accessing the token usage table.
- Improves code maintainability and reliability by enforcing stricter type checks.

## Affected Files
- `producer/utils/image_token_usage.ts`: Added types and updated function for type safety.

## Recent Change: Per-Agent Usage Tracking in Workflow

- The workflow in `producer/workflow/task_workflow.ts` now records token usage for each agent step separately by calling `setAgentUsage` after each `callAgentQueue` invocation.
- This ensures that token usage for marketing analysis, product-audience matching, context planner image generation, and brochure prompt generation is tracked and stored independently for each agent.

## Rationale
- Improves observability and cost tracking for each agent in the workflow.
- Enables more granular analytics and debugging for each step of the workflow.

## Affected Files
- `producer/workflow/task_workflow.ts`: Now calls `setAgentUsage` after each agent step in `runWorkflow`.

## Recent Change: Token Usage Reporting in Product Audience Matching Agent

- Agent `producer/agents/product_audience_matching.ts` kini mengembalikan informasi penggunaan token (field `usage`) pada setiap response API.
- Field ini berisi detail penggunaan token dari OpenAI API (prompt_tokens, completion_tokens, total_tokens, model, dsb) jika tersedia.
- Perubahan ini meningkatkan transparansi dan memungkinkan pelacakan biaya token per permintaan pada level agent.

## Rationale
- Memudahkan monitoring dan audit penggunaan token untuk setiap permintaan agent.
- Mendukung analisis biaya dan optimasi penggunaan model language API.

## Affected Files
- `producer/agents/product_audience_matching.ts`: Menambahkan pengembalian field `usage` pada response API.

## Recent Change: Guard Clause for Brochure Image Generation Prompts

- Added a guard clause at the start of `generateImagesFromBrochures` in `producer/handlers/generate_images_from_brochures.ts` to ensure the `prompts` argument is always a valid array before accessing `.length`.
- If `prompts` is not an array or is empty, the function now returns an error object instead of throwing a TypeError.

## Rationale
- Prevents runtime TypeError when the input to image generation is undefined or not an array, improving workflow robustness and error reporting.

## Affected Files
- `producer/handlers/generate_images_from_brochures.ts`: Added guard clause for prompts array validation.

## Recent Change: Type Safety Fix for cost_per_agent in Token Usage Calculation

- Updated `producer/utils/token_usage.ts` to explicitly type `usageWithCost` as `TaskUsageWithCost` in the `calculateCost` function.
- This ensures that `cost_per_agent` is recognized as an object with a string index signature, matching the `CostPerAgent` interface.
- The change resolves TypeScript linter errors related to implicit 'any' when indexing `cost_per_agent` by agent name.

## Rationale
- Enforces type safety and prevents runtime or compile-time errors due to incorrect object indexing.
- Keeps the codebase consistent with defined TypeScript interfaces and project standards.

## Affected Files
- `producer/utils/token_usage.ts`: Typing fix for `usageWithCost` in `calculateCost`.

## Recent Change: CTA Button Mapping Agent Implementation

- Implemented a new agent in `producer/agents/cta_button_mapping.ts` that processes the output of `generateImagesFromBrochures` by detecting CTA buttons in each image and returning an HTML image map string for each image, as specified in `system_prompt/cta_button_mapping.txt`.
- The agent receives an image URL and size, sends a prompt to OpenAI, and returns the HTML mapping result for use in voucher redemption flows or similar use cases.
- The system prompt is embedded directly in the agent for maintainability and consistency with other agents.

## Rationale
- Automates the process of generating HTML image maps for CTA buttons, enabling interactive voucher redemption and improved user experience.
- Ensures consistency and accuracy in CTA detection and area enlargement logic, leveraging AI for image analysis.
- Follows the modular agent pattern for maintainability and future extensibility.

## Affected Files
- `producer/agents/cta_button_mapping.ts`: New agent for CTA button mapping.
- `system_prompt/cta_button_mapping.txt`: System prompt specification for the agent.

## Recent Change: CTA Button Mapping Agent Cloudflare Worker Compatibility & Error Handling

- Updated `producer/agents/cta_button_mapping.ts` to replace Node.js Buffer usage with a browser/worker-compatible ArrayBuffer to base64 conversion using `btoa`, ensuring compatibility with Cloudflare Workers and other edge environments.
- Added explicit check for missing or invalid `OPENAI_API_KEY` in the environment, returning a clear error if not set.
- Improved error handling for OpenAI API responses: now checks for missing `choices` in the response and returns a detailed error message if the API returns an error or unexpected structure.

## Rationale
- Ensures the CTA Button Mapping agent works in Cloudflare Workers and similar environments.
- Provides clearer diagnostics for API key/configuration issues and OpenAI API errors.

## Affected Files
- `producer/agents/cta_button_mapping.ts`: Updated for compatibility and error handling improvements.

## Recent Change: Brochure Image Generation Result Enhancement

- Updated `generateImagesFromBrochures` in `producer/handlers/generate_images_from_brochures.ts` to include two new fields in the result object for each image:
  - `usage_cta`: Contains the token usage or metadata returned by the CTA Button Mapping agent for each image.
  - `cta_html`: Contains the HTML image map string generated by the CTA Button Mapping agent for each image.
- This change allows downstream consumers to access both the CTA mapping HTML and its associated usage/cost data directly from the image generation result.

## Rationale
- Improves observability and traceability of CTA mapping agent usage per image.
- Enables direct integration of CTA HTML mapping in downstream workflows without additional API calls.

## Affected Files
- `producer/handlers/generate_images_from_brochures.ts`: Now returns `usage_cta` and `cta_html` fields in the result object for each image.

## Recent Change: HTML Cleaning for CTA Button Mapping Agent

- Agent `producer/agents/cta_button_mapping.ts` kini membersihkan hasil HTML dari wrapping markdown code block (```html ... ```) sebelum mengembalikannya di field `html`.
- Hal ini memastikan field `html` selalu berisi HTML murni tanpa pembungkus markdown, sehingga lebih mudah digunakan di frontend atau proses downstream lain.

## Recent Change: Task Status Loadtime Tracking

- Pada `consumer/queues/workflow_consumer.ts`, status task sekarang menyimpan waktu proses (loadtime) dari status processing ke completed dalam satuan menit (float, dua desimal), bukan lagi milidetik. Loadtime ini juga dikirim ke webhook eksternal.
- Loadtime ini dapat digunakan untuk analisis performa dan monitoring waktu eksekusi setiap task.

## Recent Change: Webhook Notification on Task Completion

- Setelah status task selesai (completed) diupdate di KV pada `consumer/queues/workflow_consumer.ts`, sistem sekarang juga mengirim status, task_id, dan project_key ke webhook eksternal `https://ai.gass.co.id/webhook.html` menggunakan HTTP POST dengan payload JSON.
- Jika terjadi error saat pengiriman webhook, error akan dicatat di log, namun tidak mempengaruhi update status di KV.

## Rationale
- Memungkinkan integrasi eksternal untuk monitoring atau trigger otomatis berbasis status task.
- Pengiriman webhook dilakukan setelah status task completed untuk memastikan hanya status final yang dikirim.

## Affected Files
- `consumer/queues/workflow_consumer.ts`: Menambahkan logic pengiriman webhook setelah update status completed.

## Recent Change: Cloudflare Screenshot Response Handling in website_screenshot_ocr.ts

- Fixed a bug in `producer/agents/website_screenshot_ocr.ts` where the Cloudflare Browser Rendering API sometimes returns a non-JSON response (such as a data:image/jpeg;base64,... string) instead of JSON. The agent now detects if the response is JSON or a base64 image string, and handles both cases gracefully without throwing a parsing error.
- This ensures the screenshot and OCR workflow works reliably regardless of Cloudflare's response format.

## Rationale
- Cloudflare's screenshot API may return either a JSON object or a direct base64-encoded image string. Attempting to always parse as JSON caused runtime errors when the response was not JSON.
- The new logic checks the response type and extracts the base64 image data as needed, improving robustness and reliability.

## Affected Files
- `producer/agents/website_screenshot_ocr.ts`: Improved response handling for Cloudflare screenshot API.

## Recent Change: Banner Workflow Product Audience Matching Input Merge

- Pada fungsi `runWorkflow_banner` di `producer/workflow/task_workflow.ts`, kini input untuk agent product-audience-matching (productAudienceMatchingInput) dibentuk dengan menggabungkan seluruh field dari `audienceAnalysisResult.data` dan `audienceProfilingResult.data`.
- Format input yang dihasilkan:
  ```json
  {
    "product_analysis": {},
    "country_market_insights": {},
    "best_audience_segment": {},
    "audience_segments": [],
    "ad_recommendations": {
      "square_banners": [],
      "portrait_banners": []
    },
    "profiled_segment": {}
  }
  ```
- Hal ini memastikan seluruh data analisis dan profiling audience tersedia untuk proses matching produk-audience pada workflow banner.

## Rationale
- Menjamin integrasi data yang utuh dan konsisten antara hasil analisis audience dan profiling sebelum proses matching.
- Memudahkan audit dan debugging karena struktur input menjadi eksplisit dan terdokumentasi.

## Affected Files
- `producer/workflow/task_workflow.ts`: Penggabungan input pada runWorkflow_banner.

## Recent Change: Banner Image Generation Handler Supports Square and Portrait Prompts

- Updated `producer/handlers/generate_images_banner.ts` to accept an object with `square_prompts` and `portrait_prompts` arrays as input.
- The handler now validates that at least one of these arrays is present and non-empty before proceeding.
- For each non-empty prompt array, the handler generates images for all prompts in both `square_prompts` and `portrait_prompts`.
- Each result object now includes a `type` field indicating whether the image is from a square or portrait prompt.
- If neither array is present or both are empty, the handler returns an error object.

## Rationale
- This change allows for more flexible and explicit control over the types of banners generated (square and/or portrait) in a single request.
- Improves input validation and error reporting for downstream consumers.

## Affected Files
- `producer/handlers/generate_images_banner.ts`: Updated to support the new input structure and validation logic for square and portrait prompts.

## Recent Change: Anthropic API Integration for Claude Models in Agent

- `producer/agents/agent.ts` kini otomatis menggunakan API Anthropic jika parameter model mengandung kata 'claude'. Untuk model lain, tetap menggunakan OpenAI API seperti sebelumnya.
- Implementasi ini menggunakan fetch langsung ke endpoint https://api.anthropic.com/v1/messages dengan header dan body sesuai dokumentasi resmi Anthropic, tanpa dependensi SDK eksternal, sehingga tetap ringan dan kompatibel dengan Cloudflare Worker.
- Prompt system dan user digabung menjadi satu pesan user sesuai kebutuhan API Anthropic.
- Response handling tetap konsisten, mengembalikan data dan usage seperti sebelumnya.

## Rationale
- Mendukung model Claude (Anthropic) secara native tanpa perlu dependensi tambahan.
- Memastikan agent.ts tetap ringan dan kompatibel dengan lingkungan edge/worker.
- Memudahkan ekspansi ke model-model LLM lain di masa depan dengan pola deteksi model yang fleksibel.

## Affected Files
- `producer/agents/agent.ts`: Logika pemilihan endpoint dan format request/response diubah untuk mendukung Anthropic jika model Claude digunakan.

## Recent Change: Input and Result Logging to R2 Bucket

- All workflow input and result objects are now logged as JSON files to a Cloudflare R2 bucket using the `logToR2` utility in `producer/utils/r2_logger.ts`.
- The files are named as `logs/{task_id}_input.json` and `logs/{task_id}_result.json` for each task, and are accessible via public URLs.
- This logging is performed at the start and end of each workflow execution (see `runWorkflow` and `runWorkflow_banner` in `producer/workflow/task_workflow.ts`).

## Rationale
- Ensures robust, auditable, and persistent logging of all workflow inputs and results for debugging, analytics, and compliance.
- Public URLs allow for easy retrieval and sharing of logs for downstream systems or manual inspection.

## Affected Files
- `producer/utils/r2_logger.ts`: Implements the logToR2 utility for saving logs to R2.
- `producer/workflow/task_workflow.ts`: Calls logToR2 to log input and result for each workflow execution.

## Recent Change: RAG-Ready Logging Format

- Log files for workflow input and result (saved to R2 via `logToR2`) now include additional fields to support Retrieval-Augmented Generation (RAG) use cases.
- Each log file contains: `task_id`, `logType`, `timestamp`, `content` (the main data), and `metadata` (including dataType and keys of the content).
- This structure makes the logs directly usable as a RAG corpus for downstream retrieval or search pipelines.

## Affected Files
- `producer/utils/r2_logger.ts`: Now writes RAG-ready log objects.

## Recent Change: Vectorization/Embeddings Integration in Banner Workflow

- Setiap hasil agent pada workflow `runWorkflow_banner` di `producer/workflow/task_workflow.ts` kini di-vectorize menggunakan OpenAI embeddings (text-embedding-ada-002) melalui utility baru `generateEmbedding` di `producer/agents/agent.ts`.
- Embedding hasil agent (audience analysis, profiling, matching, dsb) dicatat/log ke R2 bersama data aslinya, siap untuk pipeline Retrieval-Augmented Generation (RAG) downstream.
- Format log embedding mengikuti standar RAG-ready: task_id, logType, timestamp, content, metadata, embedding.
- Utility `generateEmbedding` dapat digunakan untuk vectorization data penting lain di masa depan.

## Rationale
- Memungkinkan pencarian semantik, similarity search, dan RAG berbasis hasil agent di masa depan.
- Memudahkan audit, debugging, dan pengembangan fitur retrieval AI.
- Menyiapkan data pipeline yang siap untuk integrasi vector database atau search engine.

## Affected Files
- `producer/workflow/task_workflow.ts`: Integrasi vectorization dan logging embedding di setiap step agent pada workflow banner.
- `producer/agents/agent.ts`: Penambahan utility `generateEmbedding` untuk OpenAI embeddings.
- `producer/utils/r2_logger.ts`: Digunakan untuk logging embedding ke R2.

## Recent Change: Agent Result Caching for Workflow Optimization

- Implemented agent result caching in `producer/workflow/task_workflow.ts` for the `runWorkflow_banner` function.
- Results for `audience_analysis`, `audience_profiling`, and `product_audience_matching` are now cached using a hash of the input. If the same input is received, the workflow retrieves results from cache and skips directly to `ads-copy-generation`.
- The cache is implemented using `getAgentResultCache` and `setAgentResultCache` utilities in `producer/utils/token_usage.ts`, storing results in KV with a 1-hour TTL.
- This optimization reduces redundant agent calls, improves performance, and saves API costs.

## Rationale
- Avoids repeated computation and API calls for identical inputs, improving efficiency and reducing latency.
- Ensures that downstream steps (ads-copy-generation and beyond) can proceed immediately if prior results are cached.

## Affected Files
- `producer/workflow/task_workflow.ts`: Integrated cache logic in `runWorkflow_banner`.
- `producer/utils/token_usage.ts`: Added generic agent result cache utilities.

## Recent Change: System Prompt Update API Endpoint

- Added a new POST API endpoint `/update-system-prompt` in `producer/index.ts`.
- This endpoint allows updating a system prompt in the `env.PROMPT` KV store using x-www-form-urlencoded input.
- The request must include `agent` (as the key) and `prompt` (as the value) fields.
- Only POST requests with `application/x-www-form-urlencoded` content type are accepted. The endpoint returns a JSON response indicating success or error.

## Rationale
- Enables dynamic updating of system prompts for agents without redeploying code.
- Supports easier prompt management and experimentation.

## Affected Files
- `producer/index.ts`: Added the new API endpoint implementation.

## Recent Change: Log Workflow via LOGGER_QUEUE and Logger Worker

- Fungsi logToR2 di `producer/utils/r2_logger.ts` kini mengirim data log ke queue `LOGGER_QUEUE` alih-alih langsung menulis ke R2.
- Worker baru di `logger/log/worker.ts` bertugas menerima pesan dari queue dan menyimpan log ke R2 (Cloudflare R2 bucket) secara async.
- Format log tetap sama, namun proses penulisan ke R2 kini asynchronous dan terpisah dari workflow utama.

## Rationale
- Memisahkan proses logging dari workflow utama meningkatkan performa dan reliability.
- Logging asinkron mengurangi latensi dan potensi bottleneck pada proses utama.

## Affected Files
- `producer/utils/r2_logger.ts`: Refactor logToR2 untuk kirim ke LOGGER_QUEUE.
- `logger/log/worker.ts`: Implementasi worker consumer untuk LOGGER_QUEUE dan penulisan log ke R2.

## Recent Change: Log Links in Workflow Response

- Response dari workflow (`runWorkflow` dan `runWorkflow_banner`) kini mengembalikan field baru `log_links`.
- Field ini berisi daftar link log input/output untuk setiap agent utama, dengan format URL R2 (`https://r2-agent-ai.gass.co.id/logs/{task_id}_{step}.json`).
- Memudahkan audit, traceability, dan akses langsung ke log setiap step agent.

## Affected Files
- `producer/workflow/task_workflow.ts`: Penambahan field log_links pada return object workflow.

## Recent Change: Logger Queue Payload Structure Fix

- Fixed a bug in `logToR2` (`producer/utils/r2_logger.ts`) where the payload sent to `LOGGER_QUEUE` was not wrapped in a `body` property. Now, the payload is sent as `{ body: { task_id, logType, data } }` to match the expectation of the logger worker (`logger/log/worker.ts`).
- This prevents errors like 'Log queue message missing body' and ensures compatibility between the producer and consumer of the logger queue.

## Rationale
- Ensures that the logger worker can reliably process log messages from the queue without error.
- Maintains robust, asynchronous logging for workflow input and result logs.

## Affected Files
- `producer/utils/r2_logger.ts`: Fixed payload structure for LOGGER_QUEUE.
- `logger/log/worker.ts`: Consumes the corrected payload structure.

## Recent Change: Real-Time Task Status Streaming via SSE

- Added a new API endpoint `/check-status-sse` in `producer/index.ts` that streams task status updates to the client using Server-Sent Events (SSE).
- The handler is implemented as `stream` in `producer/handlers/check_status.ts`. It periodically (every 1 second) sends the latest status for a given `task_id` until the task is completed, failed, or a 15-minute timeout is reached (900,000 ms).
- This allows clients to receive real-time updates on task progress without polling.

## Rationale
- Improves user experience by providing instant feedback on long-running tasks.
- Reduces server load compared to frequent polling.

## Affected Files
- `producer/index.ts`: Adds the `/check-status-sse` endpoint.
- `producer/handlers/check_status.ts`: Adds the `stream` method for SSE status streaming.

## Recent Change: Multimodal OpenAI Image Generation in Banner Handler

- Updated `producer/handlers/generate_images_banner.ts` to support multimodal image generation using OpenAI's responses.create API.
- If both `logo` and `product_image` are provided, the handler now encodes these images as base64 and sends them as `input_image` content along with the prompt as `input_text` to OpenAI's multimodal endpoint.
- The handler processes the response, uploads the generated image to R2, and returns the public URL as before.
- If either `logo` or `product_image` is missing, the handler falls back to the previous logic using Cloudflare Gateway fetch for image generation.
- This change enables richer, context-aware banner image generation when reference images are available, while maintaining compatibility with the legacy workflow.

## Rationale
- Enables use of OpenAI's latest multimodal capabilities for more accurate and relevant banner image generation.
- Maintains backward compatibility and robust error handling.

## Affected Files
- `producer/handlers/generate_images_banner.ts`: Major logic update for multimodal input and OpenAI API usage.

## Recent Change: Auto-Retry & Resume for Workflow (runWorkflow)

- Implemented an auto-retry mechanism in `runWorkflow` (`producer/workflow/task_workflow.ts`).
- If a workflow fails at any agent step, the error now includes the last successful agent step (`last_success_agent`).
- The consumer/queue can re-queue the task with this info, and the workflow will resume from the last successful step, skipping previous steps.
- Steps covered: `marketing_analysis`, `product_audience_matching`, `context_planner_image_generator`, `brochure_prompt_generator`, `brochure_image_generator`.
- Each step is skipped if already completed (based on `last_success_agent`).
- This improves reliability for long-running or multi-agent workflows, reducing wasted computation and cost on partial failures.

## Rationale
- Ensures that if a workflow fails (e.g., due to network or agent error), it can be retried and resumed from the last successful step, not from the beginning.
- Reduces cost and time for recovery from partial failures.
- Makes the workflow more robust and production-ready for large/complex tasks.

## Affected Files
- `producer/workflow/task_workflow.ts`: Implements the auto-retry and resume logic in `runWorkflow`.
- (Consumer/queue logic should be updated to re-queue with `last_success_agent` on error for full effect.)

## Recent Change: Vertex AI Image Generation Test Endpoint

- Added a new API endpoint `/test-vertex-image` in `producer/index.ts` for testing Google Vertex AI image generation via Cloudflare AI Gateway.
- The endpoint accepts a POST request with JSON `{ prompt: string }`, calls Vertex AI (Imagen 3.0) through the Gateway, and returns the generated image as base64.
- This enables easy testing and validation of Vertex AI integration as an alternative/fallback to OpenAI image generation.

## Rationale
- Provides a simple, isolated way to test and debug Vertex AI image generation integration.
- Useful for fallback scenarios or for comparing output between OpenAI and Vertex AI models.

## Affected Files
- `producer/index.ts`: Added the new endpoint implementation.

## Recent Change: Add System Prompt API Endpoint

- Added a new POST API endpoint `/add-system-prompt` in `producer/index.ts`.
- This endpoint allows adding (or overwriting) a system prompt in the `env.PROMPT` KV store using x-www-form-urlencoded input.
- The request must include `agent` (as the key) and `prompt` (as the value) fields.
- Only POST requests with `application/x-www-form-urlencoded` content type are accepted. The endpoint returns a JSON response indicating success or error.
- This endpoint is semantically for adding new prompts, but will also overwrite if the key already exists.

## Rationale
- Enables dynamic addition of system prompts for agents without redeploying code.
- Supports easier prompt management and experimentation.

## Affected Files
- `producer/index.ts`: Added the new API endpoint implementation.

## Recent Change: List All System Prompt Keys API Endpoint

- Added a new GET API endpoint `/list-system-prompts` in `producer/index.ts`.
- This endpoint returns a JSON array of all keys (agent names) stored in the `env.PROMPT` KV store.
- Only GET requests are allowed; other methods return 405.

## Rationale
- Enables clients or admin tools to easily enumerate all available system prompt keys for management or display purposes.

## Affected Files
- `producer/index.ts`: Added the new API endpoint implementation.

## Recent Change: Flexible Image Generation Agent

- Added `producer/agents/agent_image.ts`, a flexible image generation agent.
- This agent automatically detects the input structure:
  - If input is an array of prompts, uses `generateImagesFromBrochures` logic.
  - If input is an object with `square_prompts`/`portrait_prompts`, uses `generateImagesBanner` logic.
  - If input has a `prompts` array or `banner_prompt` object, routes accordingly.
- Can be used for steps like:
  - `brochure_image_generator` (with prompts array)
  - `generate_images_banner` (with square/portrait prompts)
- Returns array of image results (url, usage, etc) in a unified format.
- Example JSON step:
  ```json
  {
    "system_prompt": "brochure_image_generator",
    "model": "gpt-image-1",
    "input_from_step": { "prompts": "brochure_prompt_generator.data.final_openai_prompts" }
  }
  ```
  or
  ```json
  {
    "system_prompt": "generate_images_banner",
    "model": "gpt-image-1",
    "input_template": { "banner_prompt": "${prompt-banner-generator.data}", "input": "${_input}" }
  }
  ```

## Rationale
- Simplifies workflow step definition for image generation.
- Reduces code duplication and centralizes image agent logic.

## Affected Files
- `producer/agents/agent_image.ts`: New flexible image agent.
- `producer/handlers/generate_images_from_brochures.ts`, `producer/handlers/generate_images_banner.ts`: Used as backend logic. 