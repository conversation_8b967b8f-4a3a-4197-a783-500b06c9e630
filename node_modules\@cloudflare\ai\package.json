{"name": "@cloudflare/ai", "version": "1.2.2", "type": "module", "description": "Cloudflare's Workers AI SDK", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "scripts": {"build": "rm -rf dist/ && rollup -c", "build-old": "rm -rf dist/ && tsc && esbuild src/index.ts --bundle --outfile=dist/index.js --sourcemap --format=esm", "build:for-dashboard": "npm_package_version=$npm_package_version bash ./scripts/build-for-dashboard.sh", "prepublish": "bash ./scripts/add_registry_config.sh && npm run build", "test": "NODE_NO_WARNINGS=1 mocha --require ts-node/register --config ./mocharc.json", "dry-publish": "npm pack --dry-run"}, "files": ["dist", "LICENSE", "README.md"], "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.2.1", "@rollup/plugin-typescript": "^10.0.1", "@types/mocha": "^10.0.6", "@types/mustache": "^4.2.4", "@types/node": "^20.11.19", "@types/semver": "^7.5.6", "dotenv": "^16.3.1", "mocha": "^10.3.0", "mustache": "^4.2.0", "prettier": "^2.8.8", "rollup": "^3.25.1", "rollup-plugin-bundle-size": "^1.0.3", "rollup-plugin-copy": "^3.4.0", "semver": "^7.6.0", "ts-node": "^10.9.2", "tsc-bundle": "^0.1.3", "tsup": "^7.2.0", "typescript": "^5.3.3"}}