export declare const postFuncQwen: (response: any, inputs: any) => any;
export declare const postFuncLabelScore: (r: any, inputs: any) => {
    label: string;
    score: any;
}[];
export declare const postFuncSDXLTurbo: (r: any, input: any) => Uint8Array;
export declare const postFuncWhisper: (response: any, inputs: any) => {
    text: any;
    word_count: number;
    words: any;
    vtt: string;
};
export declare const postFuncWhisperTiny: (response: any, inputs: any) => {
    text: any;
    word_count: number;
    words: any;
    vtt: string;
};
//# sourceMappingURL=processing.d.ts.map