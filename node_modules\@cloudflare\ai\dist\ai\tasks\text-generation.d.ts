import { Tensor } from "../tensor";
import { AiTask } from "./types/core";
import { AiTextGenerationInput, BaseAiTextGeneration } from "./types/tasks";
export declare class AiTextGeneration extends BaseAiTextGeneration implements AiTask {
    private modelSettings;
    preProcessedInputs: any;
    tensors: Array<Tensor<any>>;
    schema: {
        input: {
            type: string;
            oneOf: ({
                properties: {
                    prompt: {
                        type: string;
                        minLength: number;
                        maxLength: number;
                    };
                    raw: {
                        type: string;
                        default: boolean;
                    };
                    stream: {
                        type: string;
                        default: boolean;
                    };
                    max_tokens: {
                        type: string;
                        default: number;
                    };
                    temperature: {
                        type: string;
                    };
                    lora: {
                        type: string;
                    };
                    messages?: undefined;
                };
                required: string[];
            } | {
                properties: {
                    messages: {
                        type: string;
                        items: {
                            type: string;
                            properties: {
                                role: {
                                    type: string;
                                };
                                content: {
                                    type: string;
                                    maxLength: number;
                                };
                            };
                            required: string[];
                        };
                    };
                    stream: {
                        type: string;
                        default: boolean;
                    };
                    max_tokens: {
                        type: string;
                        default: number;
                    };
                    temperature: {
                        type: string;
                    };
                    prompt?: undefined;
                    raw?: undefined;
                    lora?: undefined;
                };
                required: string[];
            })[];
        };
        output: {
            oneOf: ({
                type: string;
                contentType: string;
                properties: {
                    response: {
                        type: string;
                    };
                };
                format?: undefined;
            } | {
                type: string;
                contentType: string;
                format: string;
                properties?: undefined;
            })[];
        };
    };
    constructor(inputs: AiTextGenerationInput, modelSettings: any);
    preProcessing(): void;
    generateTensors(preProcessedInputs: any): any;
    postProcessing(response: any): void;
    postProcessingStream(response: any, inclen: any): {
        response: any;
    };
}
//# sourceMappingURL=text-generation.d.ts.map