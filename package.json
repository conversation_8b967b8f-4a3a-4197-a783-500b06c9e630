{"name": "agent-cloudflare", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@cloudflare/ai": "^1.2.2", "@mistralai/mistralai": "^1.6.0", "itty-router": "^5.0.18", "openai": "^4.98.0", "uuid": "^11.1.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250507.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "typescript": "^5.0.0"}}